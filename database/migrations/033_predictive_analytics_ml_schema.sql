-- Predictive Analytics & Machine Learning Schema
-- Phase 2 Week 15-16: Advanced ML pipeline with churn prediction, revenue forecasting, and anomaly detection
-- Builds on existing cohort analysis, CLV calculation, and funnel analysis foundation

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb";

-- =====================================================
-- ML MODEL REGISTRY AND METADATA
-- =====================================================

-- ML Models registry for model management and versioning
CREATE TABLE IF NOT EXISTS ml_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Model identification
    model_name VARCHAR(255) NOT NULL,
    model_type VARCHAR(100) NOT NULL, -- 'churn_prediction', 'revenue_forecasting', 'behavior_prediction', 'anomaly_detection'
    model_version VARCHAR(50) NOT NULL,
    model_description TEXT,
    
    -- Model configuration
    algorithm VARCHAR(100) NOT NULL, -- 'random_forest', 'xgboost', 'lstm', 'arima', 'isolation_forest'
    hyperparameters JSONB DEFAULT '{}',
    feature_columns JSONB NOT NULL DEFAULT '[]', -- Array of feature column names
    target_column VARCHAR(255),
    
    -- Model performance metrics
    training_accuracy DECIMAL(5,4),
    validation_accuracy DECIMAL(5,4),
    test_accuracy DECIMAL(5,4),
    precision_score DECIMAL(5,4),
    recall_score DECIMAL(5,4),
    f1_score DECIMAL(5,4),
    auc_score DECIMAL(5,4),
    mae DECIMAL(10,4), -- Mean Absolute Error for regression
    rmse DECIMAL(10,4), -- Root Mean Square Error for regression
    
    -- Model metadata
    training_data_size INTEGER,
    training_duration_seconds INTEGER,
    model_size_bytes BIGINT,
    feature_importance JSONB DEFAULT '{}',
    
    -- Model lifecycle
    status VARCHAR(50) DEFAULT 'training', -- 'training', 'active', 'deprecated', 'failed'
    is_production BOOLEAN DEFAULT FALSE,
    deployment_date TIMESTAMPTZ,
    last_prediction_at TIMESTAMPTZ,
    
    -- A/B testing and experimentation
    experiment_id UUID,
    control_group_percentage DECIMAL(5,2) DEFAULT 50.0,
    treatment_group_percentage DECIMAL(5,2) DEFAULT 50.0,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, model_name, model_version),
    CHECK (training_accuracy >= 0 AND training_accuracy <= 1),
    CHECK (validation_accuracy >= 0 AND validation_accuracy <= 1),
    CHECK (test_accuracy >= 0 AND test_accuracy <= 1),
    CHECK (control_group_percentage + treatment_group_percentage = 100)
);

-- =====================================================
-- ML PREDICTIONS AND RESULTS
-- =====================================================

-- ML Predictions table (TimescaleDB hypertable for time-series predictions)
CREATE TABLE IF NOT EXISTS ml_predictions (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES ml_models(id) ON DELETE CASCADE,
    
    -- Prediction identification
    customer_id UUID,
    prediction_type VARCHAR(100) NOT NULL, -- 'churn_probability', 'revenue_forecast', 'next_action', 'anomaly_score'
    prediction_context VARCHAR(255), -- Additional context for the prediction
    
    -- Prediction values
    predicted_value DECIMAL(15,6), -- Main prediction value
    confidence_score DECIMAL(5,4), -- Confidence in prediction (0-1)
    probability_distribution JSONB, -- Full probability distribution for classification
    prediction_metadata JSONB DEFAULT '{}', -- Additional prediction details
    
    -- Feature values used for prediction
    feature_values JSONB NOT NULL DEFAULT '{}',
    
    -- Prediction timing
    predicted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    prediction_horizon_days INTEGER, -- How far into future this predicts
    valid_until TIMESTAMPTZ, -- When prediction expires
    
    -- Actual outcomes (for model evaluation)
    actual_value DECIMAL(15,6),
    actual_outcome_date TIMESTAMPTZ,
    prediction_accuracy DECIMAL(5,4),
    
    -- Business impact tracking
    action_taken VARCHAR(255), -- What action was taken based on prediction
    business_impact DECIMAL(15,2), -- Measured business impact
    
    -- Constraints
    CHECK (confidence_score >= 0 AND confidence_score <= 1),
    CHECK (prediction_accuracy >= 0 AND prediction_accuracy <= 1)
);

-- Convert ml_predictions to hypertable (partitioned by predicted_at)
SELECT create_hypertable(
    'ml_predictions', 
    'predicted_at',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- =====================================================
-- CHURN PREDICTION SPECIFIC TABLES
-- =====================================================

-- Customer Churn Risk Scores
CREATE TABLE IF NOT EXISTS customer_churn_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL,
    
    -- Churn prediction details
    churn_probability DECIMAL(5,4) NOT NULL, -- 0-1 probability of churning
    risk_level VARCHAR(20) NOT NULL, -- 'low', 'medium', 'high', 'critical'
    time_to_churn_days INTEGER, -- Predicted days until churn
    
    -- Risk factors
    primary_risk_factors JSONB NOT NULL DEFAULT '[]', -- Top factors contributing to churn risk
    engagement_score DECIMAL(5,4), -- Overall engagement score
    recency_score DECIMAL(5,4), -- How recently customer was active
    frequency_score DECIMAL(5,4), -- How frequently customer engages
    monetary_score DECIMAL(5,4), -- Customer monetary value
    
    -- Behavioral indicators
    days_since_last_purchase INTEGER,
    days_since_last_engagement INTEGER,
    purchase_frequency_decline DECIMAL(5,4), -- Rate of purchase frequency decline
    engagement_trend VARCHAR(20), -- 'increasing', 'stable', 'declining'
    
    -- Intervention recommendations
    recommended_actions JSONB DEFAULT '[]', -- Suggested retention actions
    intervention_priority INTEGER DEFAULT 5, -- 1-10 priority for intervention
    
    -- Prediction metadata
    model_version VARCHAR(50),
    predicted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    UNIQUE(tenant_id, customer_id, predicted_at),
    CHECK (churn_probability >= 0 AND churn_probability <= 1),
    CHECK (engagement_score >= 0 AND engagement_score <= 1),
    CHECK (intervention_priority >= 1 AND intervention_priority <= 10)
);

-- =====================================================
-- REVENUE FORECASTING TABLES
-- =====================================================

-- Revenue Forecasts
CREATE TABLE IF NOT EXISTS revenue_forecasts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Forecast identification
    forecast_name VARCHAR(255) NOT NULL,
    forecast_type VARCHAR(100) NOT NULL, -- 'daily', 'weekly', 'monthly', 'quarterly'
    forecast_horizon_days INTEGER NOT NULL,
    
    -- Forecast values
    forecasted_revenue DECIMAL(15,2) NOT NULL,
    lower_bound DECIMAL(15,2), -- Confidence interval lower bound
    upper_bound DECIMAL(15,2), -- Confidence interval upper bound
    confidence_level DECIMAL(5,4) DEFAULT 0.95, -- Confidence level (e.g., 0.95 for 95%)
    
    -- Forecast components
    trend_component DECIMAL(15,2),
    seasonal_component DECIMAL(15,2),
    residual_component DECIMAL(15,2),
    external_factors JSONB DEFAULT '{}', -- External factors affecting forecast
    
    -- Forecast metadata
    model_algorithm VARCHAR(100), -- 'arima', 'prophet', 'lstm', 'ensemble'
    training_data_points INTEGER,
    forecast_accuracy DECIMAL(5,4), -- Historical accuracy of this forecast type
    
    -- Time dimensions
    forecast_date DATE NOT NULL, -- Date this forecast is for
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, forecast_name, forecast_date),
    CHECK (forecasted_revenue >= 0),
    CHECK (confidence_level > 0 AND confidence_level <= 1)
);

-- =====================================================
-- BEHAVIOR PREDICTION TABLES
-- =====================================================

-- Customer Behavior Predictions
CREATE TABLE IF NOT EXISTS behavior_predictions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL,
    
    -- Prediction details
    behavior_type VARCHAR(100) NOT NULL, -- 'next_purchase', 'product_interest', 'channel_preference', 'timing_preference'
    predicted_behavior JSONB NOT NULL, -- Detailed prediction results
    confidence_score DECIMAL(5,4) NOT NULL,
    
    -- Next-best-action recommendations
    recommended_action VARCHAR(255),
    action_priority INTEGER DEFAULT 5, -- 1-10 priority
    expected_impact DECIMAL(15,2), -- Expected revenue/engagement impact
    
    -- Timing predictions
    predicted_timing TIMESTAMPTZ, -- When behavior is likely to occur
    optimal_contact_time TIMESTAMPTZ, -- Best time to reach customer
    
    -- Product/content recommendations
    recommended_products JSONB DEFAULT '[]',
    recommended_content JSONB DEFAULT '[]',
    recommended_channels JSONB DEFAULT '[]',
    
    -- Prediction metadata
    model_version VARCHAR(50),
    predicted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    UNIQUE(tenant_id, customer_id, behavior_type, predicted_at),
    CHECK (confidence_score >= 0 AND confidence_score <= 1),
    CHECK (action_priority >= 1 AND action_priority <= 10)
);

-- =====================================================
-- ANOMALY DETECTION TABLES
-- =====================================================

-- Anomaly Detection Results
CREATE TABLE IF NOT EXISTS anomaly_detections (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Anomaly identification
    anomaly_type VARCHAR(100) NOT NULL, -- 'revenue_spike', 'traffic_drop', 'conversion_anomaly', 'customer_behavior'
    entity_type VARCHAR(100), -- 'customer', 'product', 'campaign', 'system'
    entity_id UUID, -- ID of the affected entity
    
    -- Anomaly details
    anomaly_score DECIMAL(5,4) NOT NULL, -- 0-1 anomaly score
    severity_level VARCHAR(20) NOT NULL, -- 'low', 'medium', 'high', 'critical'
    anomaly_description TEXT,
    
    -- Anomaly metrics
    expected_value DECIMAL(15,6),
    actual_value DECIMAL(15,6),
    deviation_percentage DECIMAL(8,4),
    statistical_significance DECIMAL(5,4),
    
    -- Context and features
    contributing_factors JSONB DEFAULT '{}',
    affected_metrics JSONB DEFAULT '[]',
    time_window_start TIMESTAMPTZ,
    time_window_end TIMESTAMPTZ,
    
    -- Response and resolution
    alert_sent BOOLEAN DEFAULT FALSE,
    alert_recipients JSONB DEFAULT '[]',
    investigation_status VARCHAR(50) DEFAULT 'open', -- 'open', 'investigating', 'resolved', 'false_positive'
    resolution_notes TEXT,
    
    -- Detection metadata
    detection_algorithm VARCHAR(100),
    model_version VARCHAR(50),
    detected_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CHECK (anomaly_score >= 0 AND anomaly_score <= 1),
    CHECK (statistical_significance >= 0 AND statistical_significance <= 1)
);

-- Convert anomaly_detections to hypertable (partitioned by detected_at)
SELECT create_hypertable(
    'anomaly_detections',
    'detected_at',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- =====================================================
-- FEATURE STORE TABLES
-- =====================================================

-- Feature Store for ML feature engineering and storage
CREATE TABLE IF NOT EXISTS ml_features (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,

    -- Feature identification
    entity_id UUID NOT NULL, -- Customer, product, or other entity ID
    entity_type VARCHAR(100) NOT NULL, -- 'customer', 'product', 'campaign'
    feature_name VARCHAR(255) NOT NULL,
    feature_group VARCHAR(100), -- Group features by category

    -- Feature values
    feature_value DECIMAL(15,6),
    feature_value_text VARCHAR(1000), -- For categorical features
    feature_value_json JSONB, -- For complex features

    -- Feature metadata
    feature_type VARCHAR(50) NOT NULL, -- 'numerical', 'categorical', 'boolean', 'text', 'json'
    data_source VARCHAR(100), -- Source of the feature data
    computation_method VARCHAR(255), -- How feature was computed

    -- Feature quality metrics
    completeness_score DECIMAL(5,4), -- Percentage of non-null values
    uniqueness_score DECIMAL(5,4), -- Percentage of unique values
    validity_score DECIMAL(5,4), -- Percentage of valid values

    -- Time dimensions
    feature_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    valid_from TIMESTAMPTZ DEFAULT NOW(),
    valid_until TIMESTAMPTZ,

    -- Constraints
    CHECK (completeness_score >= 0 AND completeness_score <= 1),
    CHECK (uniqueness_score >= 0 AND uniqueness_score <= 1),
    CHECK (validity_score >= 0 AND validity_score <= 1)
);

-- Convert ml_features to hypertable (partitioned by feature_timestamp)
SELECT create_hypertable(
    'ml_features',
    'feature_timestamp',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- =====================================================
-- MODEL TRAINING AND EVALUATION TABLES
-- =====================================================

-- Model Training Jobs
CREATE TABLE IF NOT EXISTS ml_training_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    model_id UUID REFERENCES ml_models(id) ON DELETE CASCADE,

    -- Training job details
    job_name VARCHAR(255) NOT NULL,
    job_type VARCHAR(100) NOT NULL, -- 'initial_training', 'retraining', 'hyperparameter_tuning'
    training_data_query TEXT NOT NULL, -- SQL query to fetch training data

    -- Training configuration
    training_config JSONB NOT NULL DEFAULT '{}',
    hyperparameter_grid JSONB DEFAULT '{}', -- For hyperparameter tuning
    cross_validation_folds INTEGER DEFAULT 5,
    test_split_percentage DECIMAL(5,2) DEFAULT 20.0,

    -- Training progress and results
    status VARCHAR(50) DEFAULT 'queued', -- 'queued', 'running', 'completed', 'failed'
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    training_logs TEXT,
    error_message TEXT,

    -- Training metrics
    training_duration_seconds INTEGER,
    data_points_processed INTEGER,
    final_model_accuracy DECIMAL(5,4),
    best_hyperparameters JSONB,

    -- Resource usage
    cpu_usage_seconds INTEGER,
    memory_usage_mb INTEGER,

    -- Timestamps
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),

    -- Constraints
    CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    CHECK (test_split_percentage > 0 AND test_split_percentage < 100)
);

-- Model Evaluation Results
CREATE TABLE IF NOT EXISTS ml_model_evaluations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES ml_models(id) ON DELETE CASCADE,

    -- Evaluation details
    evaluation_type VARCHAR(100) NOT NULL, -- 'validation', 'test', 'production', 'a_b_test'
    evaluation_dataset VARCHAR(255), -- Description of evaluation dataset
    data_points_evaluated INTEGER,

    -- Performance metrics
    accuracy DECIMAL(5,4),
    precision DECIMAL(5,4),
    recall DECIMAL(5,4),
    f1_score DECIMAL(5,4),
    auc_roc DECIMAL(5,4),
    auc_pr DECIMAL(5,4), -- Area under Precision-Recall curve

    -- Regression metrics
    mae DECIMAL(10,4), -- Mean Absolute Error
    mse DECIMAL(10,4), -- Mean Squared Error
    rmse DECIMAL(10,4), -- Root Mean Squared Error
    r_squared DECIMAL(5,4), -- R-squared

    -- Business metrics
    business_impact DECIMAL(15,2), -- Measured business impact
    cost_savings DECIMAL(15,2), -- Cost savings from model
    revenue_impact DECIMAL(15,2), -- Revenue impact from model

    -- Detailed results
    confusion_matrix JSONB, -- For classification models
    feature_importance JSONB, -- Feature importance scores
    prediction_distribution JSONB, -- Distribution of predictions

    -- Evaluation metadata
    evaluation_notes TEXT,
    evaluated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Constraints
    CHECK (accuracy >= 0 AND accuracy <= 1),
    CHECK (precision >= 0 AND precision <= 1),
    CHECK (recall >= 0 AND recall <= 1),
    CHECK (f1_score >= 0 AND f1_score <= 1)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- ML Models indexes
CREATE INDEX IF NOT EXISTS idx_ml_models_tenant_type ON ml_models(tenant_id, model_type);
CREATE INDEX IF NOT EXISTS idx_ml_models_status ON ml_models(status, is_production);
CREATE INDEX IF NOT EXISTS idx_ml_models_created_at ON ml_models(created_at);

-- ML Predictions indexes
CREATE INDEX IF NOT EXISTS idx_ml_predictions_tenant_customer ON ml_predictions(tenant_id, customer_id);
CREATE INDEX IF NOT EXISTS idx_ml_predictions_type ON ml_predictions(prediction_type, predicted_at);
CREATE INDEX IF NOT EXISTS idx_ml_predictions_model ON ml_predictions(model_id, predicted_at);
CREATE INDEX IF NOT EXISTS idx_ml_predictions_confidence ON ml_predictions(confidence_score, predicted_at);

-- Customer Churn Scores indexes
CREATE INDEX IF NOT EXISTS idx_churn_scores_tenant_customer ON customer_churn_scores(tenant_id, customer_id);
CREATE INDEX IF NOT EXISTS idx_churn_scores_risk_level ON customer_churn_scores(risk_level, predicted_at);
CREATE INDEX IF NOT EXISTS idx_churn_scores_probability ON customer_churn_scores(churn_probability DESC, predicted_at);

-- Revenue Forecasts indexes
CREATE INDEX IF NOT EXISTS idx_revenue_forecasts_tenant_date ON revenue_forecasts(tenant_id, forecast_date);
CREATE INDEX IF NOT EXISTS idx_revenue_forecasts_type ON revenue_forecasts(forecast_type, forecast_date);

-- Behavior Predictions indexes
CREATE INDEX IF NOT EXISTS idx_behavior_predictions_tenant_customer ON behavior_predictions(tenant_id, customer_id);
CREATE INDEX IF NOT EXISTS idx_behavior_predictions_type ON behavior_predictions(behavior_type, predicted_at);
CREATE INDEX IF NOT EXISTS idx_behavior_predictions_confidence ON behavior_predictions(confidence_score DESC, predicted_at);

-- Anomaly Detections indexes
CREATE INDEX IF NOT EXISTS idx_anomaly_detections_tenant_type ON anomaly_detections(tenant_id, anomaly_type);
CREATE INDEX IF NOT EXISTS idx_anomaly_detections_severity ON anomaly_detections(severity_level, detected_at);
CREATE INDEX IF NOT EXISTS idx_anomaly_detections_entity ON anomaly_detections(entity_type, entity_id);

-- ML Features indexes
CREATE INDEX IF NOT EXISTS idx_ml_features_entity ON ml_features(entity_type, entity_id, feature_timestamp);
CREATE INDEX IF NOT EXISTS idx_ml_features_name ON ml_features(feature_name, feature_timestamp);
CREATE INDEX IF NOT EXISTS idx_ml_features_group ON ml_features(feature_group, feature_timestamp);

-- Training Jobs indexes
CREATE INDEX IF NOT EXISTS idx_training_jobs_status ON ml_training_jobs(status, created_at);
CREATE INDEX IF NOT EXISTS idx_training_jobs_model ON ml_training_jobs(model_id, created_at);

-- Model Evaluations indexes
CREATE INDEX IF NOT EXISTS idx_model_evaluations_model ON ml_model_evaluations(model_id, evaluated_at);
CREATE INDEX IF NOT EXISTS idx_model_evaluations_type ON ml_model_evaluations(evaluation_type, evaluated_at);

-- =====================================================
-- TIMESCALEDB COMPRESSION POLICIES
-- =====================================================

-- Enable compression for hypertables
ALTER TABLE ml_predictions SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'tenant_id, model_id, prediction_type',
    timescaledb.compress_orderby = 'predicted_at DESC'
);

ALTER TABLE ml_features SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'tenant_id, entity_type, feature_group',
    timescaledb.compress_orderby = 'feature_timestamp DESC'
);

ALTER TABLE anomaly_detections SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'tenant_id, anomaly_type, severity_level',
    timescaledb.compress_orderby = 'detected_at DESC'
);

-- Add compression policies (compress data older than 7 days)
SELECT add_compression_policy(
    'ml_predictions',
    INTERVAL '7 days',
    if_not_exists => TRUE
);

SELECT add_compression_policy(
    'ml_features',
    INTERVAL '7 days',
    if_not_exists => TRUE
);

SELECT add_compression_policy(
    'anomaly_detections',
    INTERVAL '7 days',
    if_not_exists => TRUE
);

-- =====================================================
-- CONTINUOUS AGGREGATES FOR PERFORMANCE
-- =====================================================

-- Daily ML prediction summary
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_ml_prediction_summary
WITH (timescaledb.continuous) AS
SELECT
    tenant_id,
    model_id,
    prediction_type,
    time_bucket('1 day', predicted_at) AS day,
    COUNT(*)::int AS total_predictions,
    AVG(confidence_score)::decimal(5,4) AS avg_confidence,
    COUNT(*) FILTER (WHERE confidence_score >= 0.8)::int AS high_confidence_predictions,
    COUNT(*) FILTER (WHERE actual_value IS NOT NULL)::int AS predictions_with_outcomes,
    AVG(prediction_accuracy) FILTER (WHERE prediction_accuracy IS NOT NULL)::decimal(5,4) AS avg_accuracy
FROM ml_predictions
GROUP BY tenant_id, model_id, prediction_type, day;

-- Daily churn risk summary
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_churn_risk_summary
WITH (timescaledb.continuous) AS
SELECT
    tenant_id,
    time_bucket('1 day', predicted_at) AS day,
    COUNT(*)::int AS total_customers_scored,
    COUNT(*) FILTER (WHERE risk_level = 'critical')::int AS critical_risk_customers,
    COUNT(*) FILTER (WHERE risk_level = 'high')::int AS high_risk_customers,
    COUNT(*) FILTER (WHERE risk_level = 'medium')::int AS medium_risk_customers,
    COUNT(*) FILTER (WHERE risk_level = 'low')::int AS low_risk_customers,
    AVG(churn_probability)::decimal(5,4) AS avg_churn_probability,
    AVG(engagement_score)::decimal(5,4) AS avg_engagement_score
FROM customer_churn_scores
GROUP BY tenant_id, day;

-- Daily anomaly detection summary
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_anomaly_summary
WITH (timescaledb.continuous) AS
SELECT
    tenant_id,
    anomaly_type,
    time_bucket('1 day', detected_at) AS day,
    COUNT(*)::int AS total_anomalies,
    COUNT(*) FILTER (WHERE severity_level = 'critical')::int AS critical_anomalies,
    COUNT(*) FILTER (WHERE severity_level = 'high')::int AS high_anomalies,
    AVG(anomaly_score)::decimal(5,4) AS avg_anomaly_score,
    COUNT(*) FILTER (WHERE alert_sent = true)::int AS alerts_sent,
    COUNT(*) FILTER (WHERE investigation_status = 'resolved')::int AS resolved_anomalies
FROM anomaly_detections
GROUP BY tenant_id, anomaly_type, day;

-- Weekly revenue forecast accuracy
CREATE MATERIALIZED VIEW IF NOT EXISTS weekly_forecast_accuracy
WITH (timescaledb.continuous) AS
SELECT
    tenant_id,
    forecast_type,
    time_bucket('1 week', forecast_date) AS week,
    COUNT(*)::int AS total_forecasts,
    AVG(forecast_accuracy)::decimal(5,4) AS avg_accuracy,
    AVG(forecasted_revenue)::decimal(15,2) AS avg_forecasted_revenue,
    STDDEV(forecast_accuracy)::decimal(5,4) AS accuracy_std_dev
FROM revenue_forecasts
WHERE forecast_accuracy IS NOT NULL
GROUP BY tenant_id, forecast_type, week;

-- Add refresh policies for continuous aggregates
SELECT add_continuous_aggregate_policy(
    'daily_ml_prediction_summary',
    start_offset => INTERVAL '3 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
    'daily_churn_risk_summary',
    start_offset => INTERVAL '3 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
    'daily_anomaly_summary',
    start_offset => INTERVAL '3 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
    'weekly_forecast_accuracy',
    start_offset => INTERVAL '2 weeks',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '6 hours',
    if_not_exists => TRUE
);

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on all ML tables
ALTER TABLE ml_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE ml_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_churn_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE revenue_forecasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE behavior_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE anomaly_detections ENABLE ROW LEVEL SECURITY;
ALTER TABLE ml_features ENABLE ROW LEVEL SECURITY;
ALTER TABLE ml_training_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE ml_model_evaluations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for tenant isolation
CREATE POLICY ml_models_tenant_isolation ON ml_models
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY ml_predictions_tenant_isolation ON ml_predictions
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY customer_churn_scores_tenant_isolation ON customer_churn_scores
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY revenue_forecasts_tenant_isolation ON revenue_forecasts
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY behavior_predictions_tenant_isolation ON behavior_predictions
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY anomaly_detections_tenant_isolation ON anomaly_detections
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY ml_features_tenant_isolation ON ml_features
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY ml_training_jobs_tenant_isolation ON ml_training_jobs
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY ml_model_evaluations_tenant_isolation ON ml_model_evaluations
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

-- =====================================================
-- DATA RETENTION POLICIES
-- =====================================================

-- Add retention policies for automated data cleanup
SELECT add_retention_policy(
    'ml_predictions',
    INTERVAL '2 years',
    if_not_exists => TRUE
);

SELECT add_retention_policy(
    'ml_features',
    INTERVAL '1 year',
    if_not_exists => TRUE
);

SELECT add_retention_policy(
    'anomaly_detections',
    INTERVAL '1 year',
    if_not_exists => TRUE
);

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE ml_models IS 'Registry for ML models with versioning and performance tracking';
COMMENT ON TABLE ml_predictions IS 'Time-series storage for ML predictions with confidence scores';
COMMENT ON TABLE customer_churn_scores IS 'Customer churn risk assessments with intervention recommendations';
COMMENT ON TABLE revenue_forecasts IS 'Revenue forecasting with confidence intervals and components';
COMMENT ON TABLE behavior_predictions IS 'Customer behavior predictions and next-best-action recommendations';
COMMENT ON TABLE anomaly_detections IS 'Real-time anomaly detection results with alerting';
COMMENT ON TABLE ml_features IS 'Feature store for ML feature engineering and storage';
COMMENT ON TABLE ml_training_jobs IS 'ML model training job tracking and management';
COMMENT ON TABLE ml_model_evaluations IS 'Model evaluation results and performance metrics';

COMMIT;
