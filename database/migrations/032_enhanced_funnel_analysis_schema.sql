-- Enhanced Funnel Analysis Schema
-- Phase 2 Week 13-14: Advanced funnel analysis with conversion optimization
-- Builds on existing cohort analysis and CLV calculation foundation

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb";

-- Funnel Definitions with advanced configuration
CREATE TABLE IF NOT EXISTS funnel_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Funnel identification
    funnel_name VARCHAR(255) NOT NULL,
    funnel_description TEXT,
    funnel_type VARCHAR(50) NOT NULL, -- 'conversion', 'engagement', 'retention', 'custom'
    
    -- Funnel configuration
    steps_configuration JSONB NOT NULL DEFAULT '[]', -- Array of step definitions
    conversion_window_hours INTEGER NOT NULL DEFAULT 24, -- Time window for conversion
    allow_step_skipping BOOLEAN DEFAULT FALSE, -- Allow users to skip intermediate steps
    require_sequential_order BOOLEAN DEFAULT TRUE, -- Require steps in order
    
    -- Business rules and filters
    customer_filters JSONB DEFAULT '{}', -- Filters for funnel participants
    event_filters JSONB DEFAULT '{}', -- Filters for qualifying events
    attribution_model VARCHAR(50) DEFAULT 'first_touch', -- 'first_touch', 'last_touch', 'linear'
    
    -- Funnel metadata
    category VARCHAR(100), -- Funnel category for organization
    tags JSONB DEFAULT '[]', -- Tags for filtering and search
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Performance tracking
    total_participants INTEGER DEFAULT 0,
    total_conversions INTEGER DEFAULT 0,
    overall_conversion_rate DECIMAL(5,4) DEFAULT 0,
    last_calculated_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, funnel_name),
    CHECK (conversion_window_hours > 0),
    CHECK (overall_conversion_rate >= 0 AND overall_conversion_rate <= 1)
);

-- Funnel Steps with detailed configuration
CREATE TABLE IF NOT EXISTS funnel_steps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    funnel_id UUID NOT NULL REFERENCES funnel_definitions(id) ON DELETE CASCADE,
    
    -- Step identification
    step_name VARCHAR(255) NOT NULL,
    step_description TEXT,
    step_order INTEGER NOT NULL, -- Order within the funnel (1, 2, 3, ...)
    step_type VARCHAR(50) NOT NULL, -- 'entry', 'intermediate', 'conversion', 'exit'
    
    -- Step configuration
    event_criteria JSONB NOT NULL, -- Criteria for step completion
    required_properties JSONB DEFAULT '{}', -- Required event properties
    optional_properties JSONB DEFAULT '{}', -- Optional event properties for enrichment
    
    -- Step behavior
    is_required BOOLEAN DEFAULT TRUE, -- Whether step is required for funnel completion
    can_repeat BOOLEAN DEFAULT FALSE, -- Whether users can repeat this step
    max_time_to_complete_hours INTEGER, -- Maximum time allowed for this step
    
    -- Performance metrics
    total_entries INTEGER DEFAULT 0,
    total_completions INTEGER DEFAULT 0,
    completion_rate DECIMAL(5,4) DEFAULT 0,
    avg_time_to_complete_seconds INTEGER DEFAULT 0,
    drop_off_count INTEGER DEFAULT 0,
    drop_off_rate DECIMAL(5,4) DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, funnel_id, step_order),
    CHECK (step_order > 0),
    CHECK (completion_rate >= 0 AND completion_rate <= 1),
    CHECK (drop_off_rate >= 0 AND drop_off_rate <= 1),
    CHECK (max_time_to_complete_hours IS NULL OR max_time_to_complete_hours > 0)
);

-- Conversion Events (Time-series for funnel tracking)
CREATE TABLE IF NOT EXISTS conversion_events (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    funnel_id UUID NOT NULL REFERENCES funnel_definitions(id) ON DELETE CASCADE,
    step_id UUID NOT NULL REFERENCES funnel_steps(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    
    -- Event identification
    session_id VARCHAR(255),
    event_type VARCHAR(100) NOT NULL,
    event_source VARCHAR(50) NOT NULL, -- 'website', 'mobile_app', 'api', 'email'
    
    -- Event data
    event_properties JSONB DEFAULT '{}',
    event_value DECIMAL(12,2) DEFAULT 0, -- Monetary value associated with event
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Funnel context
    funnel_session_id UUID, -- Groups events within a funnel session
    step_completion_status VARCHAR(20) NOT NULL, -- 'completed', 'partial', 'failed', 'skipped'
    time_since_funnel_start_seconds INTEGER,
    time_since_previous_step_seconds INTEGER,
    
    -- Attribution and tracking
    traffic_source VARCHAR(100),
    campaign_id VARCHAR(255),
    referrer TEXT,
    user_agent TEXT,
    ip_address INET,
    
    -- Device and location
    device_type VARCHAR(50),
    browser VARCHAR(100),
    os VARCHAR(100),
    country VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    
    -- Timestamps
    event_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CHECK (event_value >= 0),
    CHECK (time_since_funnel_start_seconds >= 0),
    CHECK (time_since_previous_step_seconds >= 0),
    
    PRIMARY KEY (id, event_timestamp)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('conversion_events', 'event_timestamp', 
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Funnel Analytics (Aggregated metrics and insights)
CREATE TABLE IF NOT EXISTS funnel_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    funnel_id UUID NOT NULL REFERENCES funnel_definitions(id) ON DELETE CASCADE,
    
    -- Analysis period
    analysis_date DATE NOT NULL,
    analysis_period VARCHAR(20) NOT NULL, -- 'daily', 'weekly', 'monthly'
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    
    -- Funnel performance metrics
    total_participants INTEGER NOT NULL DEFAULT 0,
    total_conversions INTEGER NOT NULL DEFAULT 0,
    overall_conversion_rate DECIMAL(5,4) NOT NULL DEFAULT 0,
    
    -- Step-by-step metrics
    step_metrics JSONB NOT NULL DEFAULT '{}', -- Detailed metrics for each step
    drop_off_analysis JSONB DEFAULT '{}', -- Drop-off points and reasons
    conversion_paths JSONB DEFAULT '{}', -- Common conversion paths
    
    -- Performance insights
    avg_time_to_convert_seconds INTEGER DEFAULT 0,
    median_time_to_convert_seconds INTEGER DEFAULT 0,
    fastest_conversion_seconds INTEGER DEFAULT 0,
    slowest_conversion_seconds INTEGER DEFAULT 0,
    
    -- Segmentation analysis
    segment_performance JSONB DEFAULT '{}', -- Performance by customer segments
    traffic_source_performance JSONB DEFAULT '{}', -- Performance by traffic source
    device_performance JSONB DEFAULT '{}', -- Performance by device type
    
    -- Revenue and value metrics
    total_revenue DECIMAL(12,2) DEFAULT 0,
    avg_revenue_per_conversion DECIMAL(10,2) DEFAULT 0,
    revenue_by_step JSONB DEFAULT '{}',
    
    -- Optimization insights
    bottleneck_steps JSONB DEFAULT '[]', -- Steps with highest drop-off
    optimization_opportunities JSONB DEFAULT '[]', -- Recommended improvements
    performance_trends JSONB DEFAULT '{}', -- Trend analysis
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, funnel_id, analysis_date, analysis_period),
    CHECK (total_participants >= 0),
    CHECK (total_conversions >= 0),
    CHECK (total_conversions <= total_participants),
    CHECK (overall_conversion_rate >= 0 AND overall_conversion_rate <= 1),
    CHECK (total_revenue >= 0),
    CHECK (avg_revenue_per_conversion >= 0)
);

-- Funnel Sessions (Track individual customer journeys)
CREATE TABLE IF NOT EXISTS funnel_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    funnel_id UUID NOT NULL REFERENCES funnel_definitions(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    
    -- Session identification
    session_id VARCHAR(255) NOT NULL,
    funnel_session_id UUID NOT NULL DEFAULT gen_random_uuid(),
    
    -- Session status
    session_status VARCHAR(20) NOT NULL DEFAULT 'active', -- 'active', 'completed', 'abandoned', 'expired'
    current_step_id UUID REFERENCES funnel_steps(id),
    current_step_order INTEGER DEFAULT 1,
    
    -- Session metrics
    steps_completed INTEGER DEFAULT 0,
    total_steps INTEGER NOT NULL,
    completion_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- Timing information
    session_start_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    session_end_time TIMESTAMPTZ,
    total_session_duration_seconds INTEGER,
    time_to_conversion_seconds INTEGER,
    
    -- Session context
    entry_point VARCHAR(255), -- Where the customer entered the funnel
    traffic_source VARCHAR(100),
    campaign_id VARCHAR(255),
    device_info JSONB DEFAULT '{}',
    
    -- Revenue tracking
    session_value DECIMAL(12,2) DEFAULT 0,
    conversion_value DECIMAL(12,2) DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, funnel_session_id),
    CHECK (steps_completed >= 0),
    CHECK (steps_completed <= total_steps),
    CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    CHECK (session_value >= 0),
    CHECK (conversion_value >= 0)
);

-- Indexes for optimal performance
CREATE INDEX IF NOT EXISTS idx_funnel_definitions_tenant ON funnel_definitions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_funnel_definitions_active ON funnel_definitions(tenant_id, is_active);
CREATE INDEX IF NOT EXISTS idx_funnel_definitions_type ON funnel_definitions(tenant_id, funnel_type);

CREATE INDEX IF NOT EXISTS idx_funnel_steps_funnel ON funnel_steps(tenant_id, funnel_id);
CREATE INDEX IF NOT EXISTS idx_funnel_steps_order ON funnel_steps(tenant_id, funnel_id, step_order);
CREATE INDEX IF NOT EXISTS idx_funnel_steps_type ON funnel_steps(tenant_id, step_type);

CREATE INDEX IF NOT EXISTS idx_conversion_events_tenant_time ON conversion_events(tenant_id, event_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_conversion_events_funnel ON conversion_events(tenant_id, funnel_id, event_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_conversion_events_customer ON conversion_events(tenant_id, customer_id, event_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_conversion_events_session ON conversion_events(tenant_id, funnel_session_id);
CREATE INDEX IF NOT EXISTS idx_conversion_events_step ON conversion_events(tenant_id, step_id, event_timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_funnel_analytics_tenant ON funnel_analytics(tenant_id);
CREATE INDEX IF NOT EXISTS idx_funnel_analytics_funnel_date ON funnel_analytics(tenant_id, funnel_id, analysis_date DESC);
CREATE INDEX IF NOT EXISTS idx_funnel_analytics_period ON funnel_analytics(tenant_id, analysis_period, analysis_date DESC);

CREATE INDEX IF NOT EXISTS idx_funnel_sessions_tenant ON funnel_sessions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_funnel_sessions_funnel ON funnel_sessions(tenant_id, funnel_id);
CREATE INDEX IF NOT EXISTS idx_funnel_sessions_customer ON funnel_sessions(tenant_id, customer_id);
CREATE INDEX IF NOT EXISTS idx_funnel_sessions_status ON funnel_sessions(tenant_id, session_status);
CREATE INDEX IF NOT EXISTS idx_funnel_sessions_active ON funnel_sessions(tenant_id, funnel_id, session_status) WHERE session_status = 'active';

-- Continuous aggregates for performance optimization
CREATE MATERIALIZED VIEW IF NOT EXISTS funnel_daily_aggregates
WITH (timescaledb.continuous) AS
SELECT 
    tenant_id,
    funnel_id,
    time_bucket('1 day', event_timestamp) AS day,
    step_id,
    COUNT(*) as total_events,
    COUNT(DISTINCT customer_id) as unique_customers,
    COUNT(DISTINCT funnel_session_id) as unique_sessions,
    COUNT(*) FILTER (WHERE step_completion_status = 'completed') as completed_events,
    COUNT(*) FILTER (WHERE step_completion_status = 'failed') as failed_events,
    AVG(event_value) as avg_event_value,
    SUM(event_value) as total_event_value,
    AVG(time_since_previous_step_seconds) as avg_step_duration
FROM conversion_events
GROUP BY tenant_id, funnel_id, day, step_id;

-- Refresh policy for continuous aggregates
SELECT add_continuous_aggregate_policy('funnel_daily_aggregates',
    start_offset => INTERVAL '1 month',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Data retention policy for conversion events (keep 1 year)
SELECT add_retention_policy('conversion_events', INTERVAL '1 year', if_not_exists => TRUE);

-- Triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_funnel_definitions_updated_at BEFORE UPDATE ON funnel_definitions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_funnel_steps_updated_at BEFORE UPDATE ON funnel_steps 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_funnel_analytics_updated_at BEFORE UPDATE ON funnel_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_funnel_sessions_updated_at BEFORE UPDATE ON funnel_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) for multi-tenant isolation
ALTER TABLE funnel_definitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE funnel_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversion_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE funnel_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE funnel_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY funnel_definitions_tenant_isolation ON funnel_definitions
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY funnel_steps_tenant_isolation ON funnel_steps
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY conversion_events_tenant_isolation ON conversion_events
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY funnel_analytics_tenant_isolation ON funnel_analytics
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY funnel_sessions_tenant_isolation ON funnel_sessions
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);
