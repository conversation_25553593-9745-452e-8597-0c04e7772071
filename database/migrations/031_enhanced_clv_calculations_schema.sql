-- Enhanced CLV (Customer Lifetime Value) Calculations Schema
-- Phase 2 Week 11-12: Advanced CLV prediction models with ML integration
-- Builds on existing cohort analysis foundation

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb";

-- CLV Predictions with advanced modeling
CREATE TABLE IF NOT EXISTS clv_predictions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    
    -- Historical CLV data
    historical_clv DECIMAL(12,2) NOT NULL DEFAULT 0,
    current_clv DECIMAL(12,2) NOT NULL DEFAULT 0,
    
    -- Prediction models results
    predicted_clv_12m DECIMAL(12,2) NOT NULL DEFAULT 0, -- 12 month prediction
    predicted_clv_24m DECIMAL(12,2) NOT NULL DEFAULT 0, -- 24 month prediction
    predicted_clv_lifetime DECIMAL(12,2) NOT NULL DEFAULT 0, -- Lifetime prediction
    
    -- Model confidence and metadata
    prediction_confidence DECIMAL(5,4) NOT NULL DEFAULT 0, -- 0-1 confidence score
    model_version VARCHAR(50) NOT NULL DEFAULT 'v1.0',
    model_type VARCHAR(100) NOT NULL, -- 'traditional', 'ml_ensemble', 'deep_learning'
    
    -- Segmentation and risk assessment
    clv_segment VARCHAR(50) NOT NULL, -- 'VIP', 'High Value', 'Medium Value', 'Low Value', 'At Risk'
    churn_probability DECIMAL(5,4) NOT NULL DEFAULT 0,
    retention_probability DECIMAL(5,4) NOT NULL DEFAULT 0,
    
    -- RFM analysis integration
    rfm_score VARCHAR(10), -- e.g., '555', '111'
    recency_score INTEGER CHECK (recency_score BETWEEN 1 AND 5),
    frequency_score INTEGER CHECK (frequency_score BETWEEN 1 AND 5),
    monetary_score INTEGER CHECK (monetary_score BETWEEN 1 AND 5),
    
    -- Behavioral predictions
    predicted_orders_12m INTEGER DEFAULT 0,
    predicted_avg_order_value DECIMAL(10,2) DEFAULT 0,
    next_purchase_probability DECIMAL(5,4) DEFAULT 0,
    days_to_next_purchase INTEGER,
    
    -- Model features snapshot (for explainability)
    model_features JSONB DEFAULT '{}',
    feature_importance JSONB DEFAULT '{}',
    
    -- Timestamps
    prediction_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    data_cutoff_date TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, customer_id, prediction_date),
    CHECK (predicted_clv_12m >= 0),
    CHECK (predicted_clv_24m >= 0),
    CHECK (predicted_clv_lifetime >= 0),
    CHECK (prediction_confidence >= 0 AND prediction_confidence <= 1),
    CHECK (churn_probability >= 0 AND churn_probability <= 1),
    CHECK (retention_probability >= 0 AND retention_probability <= 1)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('clv_predictions', 'prediction_date', 
    chunk_time_interval => INTERVAL '1 month',
    if_not_exists => TRUE
);

-- CLV Segments with advanced analytics
CREATE TABLE IF NOT EXISTS clv_segments_enhanced (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Segment definition
    segment_name VARCHAR(100) NOT NULL,
    segment_type VARCHAR(50) NOT NULL, -- 'value_based', 'behavioral', 'predictive', 'risk_based'
    segment_tier VARCHAR(20) NOT NULL, -- 'VIP', 'High', 'Medium', 'Low', 'At Risk'
    
    -- CLV ranges and thresholds
    min_clv DECIMAL(12,2) NOT NULL DEFAULT 0,
    max_clv DECIMAL(12,2),
    avg_clv DECIMAL(12,2) NOT NULL DEFAULT 0,
    median_clv DECIMAL(12,2) NOT NULL DEFAULT 0,
    
    -- Segment metrics
    customer_count INTEGER NOT NULL DEFAULT 0,
    total_segment_value DECIMAL(15,2) NOT NULL DEFAULT 0,
    avg_churn_probability DECIMAL(5,4) NOT NULL DEFAULT 0,
    avg_retention_probability DECIMAL(5,4) NOT NULL DEFAULT 0,
    
    -- Behavioral characteristics
    avg_order_frequency DECIMAL(8,2) DEFAULT 0,
    avg_order_value DECIMAL(10,2) DEFAULT 0,
    avg_customer_lifespan_days INTEGER DEFAULT 0,
    
    -- Predictive metrics
    predicted_growth_rate DECIMAL(5,4) DEFAULT 0, -- Expected segment growth
    revenue_contribution_pct DECIMAL(5,4) DEFAULT 0, -- % of total revenue
    
    -- Segment criteria and rules
    segment_criteria JSONB NOT NULL DEFAULT '{}',
    business_rules JSONB DEFAULT '{}',
    
    -- Performance tracking
    segment_performance JSONB DEFAULT '{}', -- Historical performance metrics
    last_calculated_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, segment_name, segment_type),
    CHECK (min_clv >= 0),
    CHECK (max_clv IS NULL OR max_clv >= min_clv),
    CHECK (customer_count >= 0),
    CHECK (avg_churn_probability >= 0 AND avg_churn_probability <= 1),
    CHECK (avg_retention_probability >= 0 AND avg_retention_probability <= 1)
);

-- CLV Model Artifacts and Metadata
CREATE TABLE IF NOT EXISTS clv_model_artifacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Model identification
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    model_type VARCHAR(100) NOT NULL, -- 'bg_nbd', 'gamma_gamma', 'random_forest', 'xgboost', 'neural_network'
    
    -- Model performance metrics
    training_accuracy DECIMAL(5,4),
    validation_accuracy DECIMAL(5,4),
    test_accuracy DECIMAL(5,4),
    mae DECIMAL(10,2), -- Mean Absolute Error
    rmse DECIMAL(10,2), -- Root Mean Square Error
    r2_score DECIMAL(5,4), -- R-squared
    
    -- Model configuration
    hyperparameters JSONB DEFAULT '{}',
    feature_columns JSONB DEFAULT '[]',
    target_column VARCHAR(100),
    
    -- Training metadata
    training_data_size INTEGER,
    training_date_range JSONB, -- {start_date, end_date}
    training_duration_seconds INTEGER,
    
    -- Model artifacts storage
    model_file_path TEXT, -- Path to serialized model
    scaler_file_path TEXT, -- Path to feature scaler
    feature_importance JSONB DEFAULT '{}',
    
    -- Model status and lifecycle
    status VARCHAR(50) NOT NULL DEFAULT 'training', -- 'training', 'active', 'deprecated', 'failed'
    is_production BOOLEAN DEFAULT FALSE,
    deployment_date TIMESTAMPTZ,
    deprecation_date TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, model_name, model_version),
    CHECK (training_accuracy IS NULL OR (training_accuracy >= 0 AND training_accuracy <= 1)),
    CHECK (validation_accuracy IS NULL OR (validation_accuracy >= 0 AND validation_accuracy <= 1)),
    CHECK (test_accuracy IS NULL OR (test_accuracy >= 0 AND test_accuracy <= 1))
);

-- CLV Calculation Jobs and Batch Processing
CREATE TABLE IF NOT EXISTS clv_calculation_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Job identification
    job_name VARCHAR(100) NOT NULL,
    job_type VARCHAR(50) NOT NULL, -- 'full_recalculation', 'incremental_update', 'model_training'
    
    -- Job parameters
    calculation_parameters JSONB DEFAULT '{}',
    customer_filter JSONB DEFAULT '{}', -- Filters for which customers to process
    
    -- Job status and progress
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- 'pending', 'running', 'completed', 'failed', 'cancelled'
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    customers_processed INTEGER DEFAULT 0,
    total_customers INTEGER DEFAULT 0,
    
    -- Performance metrics
    execution_time_seconds INTEGER,
    memory_usage_mb INTEGER,
    cpu_usage_percentage DECIMAL(5,2),
    
    -- Results and errors
    results_summary JSONB DEFAULT '{}',
    error_message TEXT,
    error_details JSONB DEFAULT '{}',
    
    -- Timestamps
    scheduled_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    CHECK (customers_processed >= 0),
    CHECK (total_customers >= 0),
    CHECK (customers_processed <= total_customers OR total_customers = 0)
);

-- Indexes for optimal performance
CREATE INDEX IF NOT EXISTS idx_clv_predictions_tenant_customer ON clv_predictions(tenant_id, customer_id);
CREATE INDEX IF NOT EXISTS idx_clv_predictions_prediction_date ON clv_predictions(prediction_date DESC);
CREATE INDEX IF NOT EXISTS idx_clv_predictions_segment ON clv_predictions(tenant_id, clv_segment);
CREATE INDEX IF NOT EXISTS idx_clv_predictions_churn_risk ON clv_predictions(tenant_id, churn_probability DESC);
CREATE INDEX IF NOT EXISTS idx_clv_predictions_value_tier ON clv_predictions(tenant_id, predicted_clv_lifetime DESC);

CREATE INDEX IF NOT EXISTS idx_clv_segments_enhanced_tenant ON clv_segments_enhanced(tenant_id);
CREATE INDEX IF NOT EXISTS idx_clv_segments_enhanced_type ON clv_segments_enhanced(tenant_id, segment_type);
CREATE INDEX IF NOT EXISTS idx_clv_segments_enhanced_tier ON clv_segments_enhanced(tenant_id, segment_tier);
CREATE INDEX IF NOT EXISTS idx_clv_segments_enhanced_value_range ON clv_segments_enhanced(tenant_id, min_clv, max_clv);

CREATE INDEX IF NOT EXISTS idx_clv_model_artifacts_tenant ON clv_model_artifacts(tenant_id);
CREATE INDEX IF NOT EXISTS idx_clv_model_artifacts_active ON clv_model_artifacts(tenant_id, status, is_production);
CREATE INDEX IF NOT EXISTS idx_clv_model_artifacts_type ON clv_model_artifacts(tenant_id, model_type);

CREATE INDEX IF NOT EXISTS idx_clv_calculation_jobs_tenant ON clv_calculation_jobs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_clv_calculation_jobs_status ON clv_calculation_jobs(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_clv_calculation_jobs_scheduled ON clv_calculation_jobs(scheduled_at);

-- Continuous aggregates for performance optimization
CREATE MATERIALIZED VIEW IF NOT EXISTS clv_daily_aggregates
WITH (timescaledb.continuous) AS
SELECT 
    tenant_id,
    time_bucket('1 day', prediction_date) AS day,
    clv_segment,
    COUNT(*) as customer_count,
    AVG(predicted_clv_12m) as avg_predicted_clv_12m,
    AVG(predicted_clv_lifetime) as avg_predicted_clv_lifetime,
    AVG(churn_probability) as avg_churn_probability,
    AVG(prediction_confidence) as avg_prediction_confidence,
    SUM(predicted_clv_lifetime) as total_predicted_value
FROM clv_predictions
GROUP BY tenant_id, day, clv_segment;

-- Refresh policy for continuous aggregates
SELECT add_continuous_aggregate_policy('clv_daily_aggregates',
    start_offset => INTERVAL '1 month',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Data retention policy for CLV predictions (keep 2 years)
SELECT add_retention_policy('clv_predictions', INTERVAL '2 years', if_not_exists => TRUE);

-- Triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_clv_predictions_updated_at BEFORE UPDATE ON clv_predictions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_clv_segments_enhanced_updated_at BEFORE UPDATE ON clv_segments_enhanced 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_clv_model_artifacts_updated_at BEFORE UPDATE ON clv_model_artifacts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_clv_calculation_jobs_updated_at BEFORE UPDATE ON clv_calculation_jobs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) for multi-tenant isolation
ALTER TABLE clv_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE clv_segments_enhanced ENABLE ROW LEVEL SECURITY;
ALTER TABLE clv_model_artifacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE clv_calculation_jobs ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY clv_predictions_tenant_isolation ON clv_predictions
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY clv_segments_enhanced_tenant_isolation ON clv_segments_enhanced
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY clv_model_artifacts_tenant_isolation ON clv_model_artifacts
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY clv_calculation_jobs_tenant_isolation ON clv_calculation_jobs
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);
