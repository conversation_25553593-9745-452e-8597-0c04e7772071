// Enhanced Cohort Analysis Service - Phase 2 Implementation
// Advanced customer segmentation and retention modeling with TimescaleDB optimization

import { query } from "../utils/database.ts";

export interface CohortSegment {
  cohortMonth: string;
  segmentName: string;
  customerCount: number;
  totalRevenue: number;
  retentionRates: Record<string, number>;
  avgOrderValue: number;
  churnProbability: number;
}

export interface RetentionData {
  period: number;
  retentionRate: number;
  customerCount: number;
  revenue: number;
}

export interface CohortAnalysisResult {
  segments: CohortSegment[];
  retentionCurves: Record<string, RetentionData[]>;
  overview: {
    totalCohorts: number;
    avgRetentionRate: number;
    bestPerformingCohort: string;
    totalCustomers: number;
    totalRevenue: number;
  };
  predictiveInsights: {
    expectedChurn: number;
    revenueProjection: number;
    retentionTrend: 'improving' | 'declining' | 'stable';
  };
  generatedAt: string;
}

export interface CohortAnalysisOptions {
  tenantId: string;
  dateFrom: string;
  dateTo: string;
  cohortType: 'acquisition' | 'behavioral' | 'value';
  granularity: 'daily' | 'weekly' | 'monthly';
  includeProjections: boolean;
}

export class CohortAnalysisService {
  /**
   * Perform enhanced cohort analysis with predictive capabilities
   */
  async analyzeCohorts(options: CohortAnalysisOptions): Promise<CohortAnalysisResult> {
    const startTime = performance.now();
    
    try {
      // Build cohort segments with TimescaleDB optimization
      const segments = await this.buildCohortSegments(options);
      
      // Calculate retention curves for each cohort
      const retentionCurves = await this.calculateRetentionCurves(options);
      
      // Generate overview metrics
      const overview = this.generateOverviewMetrics(options, segments);

      // Calculate predictive insights
      const predictiveInsights = this.generatePredictiveInsights(options, segments);
      
      const executionTime = performance.now() - startTime;
      console.log(`Cohort analysis completed in ${executionTime.toFixed(2)}ms`);
      
      return {
        segments,
        retentionCurves,
        overview,
        predictiveInsights,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error in cohort analysis:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Cohort analysis failed: ${errorMessage}`);
    }
  }

  /**
   * Build cohort segments with advanced segmentation algorithms
   */
  private async buildCohortSegments(options: CohortAnalysisOptions): Promise<CohortSegment[]> {
    const granularityTrunc = this.getDateTruncFunction(options.granularity);

    // Build segment classification logic based on cohort type
    const getSegmentClassification = (cohortType: string) => {
      switch (cohortType) {
        case 'value':
          return `
            CASE
              WHEN lifetime_value >= 1000 THEN 'VIP'
              WHEN lifetime_value >= 500 THEN 'High Value'
              WHEN lifetime_value >= 100 THEN 'Medium Value'
              ELSE 'Low Value'
            END`;
        case 'behavioral':
          return `
            CASE
              WHEN total_orders >= 10 THEN 'Frequent Buyer'
              WHEN total_orders >= 5 THEN 'Regular Customer'
              WHEN total_orders >= 2 THEN 'Repeat Customer'
              ELSE 'One-time Buyer'
            END`;
        default:
          return `'Acquisition Cohort'`;
      }
    };

    const segmentClassification = getSegmentClassification(options.cohortType);

    const segmentQuery = `
      WITH cohort_base AS (
        SELECT
          c.id as customer_id,
          c.email,
          c.acquisition_date,
          COALESCE(c.lifetime_value, 0) as lifetime_value,
          COALESCE(c.total_orders, 0) as total_orders,
          COALESCE(c.total_spent, 0) as total_spent,
          COALESCE(c.avg_order_value, 0) as avg_order_value,
          COALESCE(c.churn_probability, 0) as churn_probability,
          ${granularityTrunc}c.acquisition_date) as cohort_period
        FROM customers c
        WHERE c.tenant_id = $1
          AND c.acquisition_date >= $2::timestamptz
          AND c.acquisition_date <= $3::timestamptz
      ),
      cohort_with_segments AS (
        SELECT
          cb.*,
          ${segmentClassification} as segment_name
        FROM cohort_base cb
      ),
      cohort_segments AS (
        SELECT
          cohort_period,
          segment_name,
          COUNT(*) as customer_count,
          SUM(lifetime_value) as total_revenue,
          AVG(avg_order_value) as avg_order_value,
          AVG(churn_probability) as avg_churn_probability
        FROM cohort_with_segments
        GROUP BY cohort_period, segment_name
      ),
      retention_calculations AS (
        SELECT
          cs.*,
          -- Calculate retention rates for different periods using sophisticated business logic
          COALESCE(
            (SELECT COUNT(DISTINCT o.customer_id)::float / cs.customer_count
             FROM orders o
             JOIN cohort_with_segments cws ON o.customer_id::uuid = cws.customer_id
             WHERE o.created_at >= cws.cohort_period + INTERVAL '1 month'
               AND o.created_at < cws.cohort_period + INTERVAL '2 months'
               AND cs.cohort_period = cws.cohort_period
               AND cs.segment_name = cws.segment_name
            ), 0
          ) as retention_month_1,
          COALESCE(
            (SELECT COUNT(DISTINCT o.customer_id)::float / cs.customer_count
             FROM orders o
             JOIN cohort_with_segments cws ON o.customer_id::uuid = cws.customer_id
             WHERE o.created_at >= cws.cohort_period + INTERVAL '3 months'
               AND o.created_at < cws.cohort_period + INTERVAL '4 months'
               AND cs.cohort_period = cws.cohort_period
               AND cs.segment_name = cws.segment_name
            ), 0
          ) as retention_month_3,
          COALESCE(
            (SELECT COUNT(DISTINCT o.customer_id)::float / cs.customer_count
             FROM orders o
             JOIN cohort_with_segments cws ON o.customer_id::uuid = cws.customer_id
             WHERE o.created_at >= cws.cohort_period + INTERVAL '6 months'
               AND o.created_at < cws.cohort_period + INTERVAL '7 months'
               AND cs.cohort_period = cws.cohort_period
               AND cs.segment_name = cws.segment_name
            ), 0
          ) as retention_month_6
        FROM cohort_segments cs
      )
      SELECT 
        cohort_period::text as cohort_month,
        segment_name,
        customer_count,
        total_revenue,
        avg_order_value,
        avg_churn_probability,
        json_build_object(
          'month_1', ROUND((retention_month_1 * 100)::numeric, 2),
          'month_3', ROUND((retention_month_3 * 100)::numeric, 2),
          'month_6', ROUND((retention_month_6 * 100)::numeric, 2)
        ) as retention_rates
      FROM retention_calculations
      ORDER BY cohort_period DESC, total_revenue DESC
    `;

    const result = await query(segmentQuery, [options.tenantId, options.dateFrom, options.dateTo], options.tenantId);
    
    return (result as Record<string, unknown>[]).map(row => ({
      cohortMonth: String(row.cohort_month),
      segmentName: String(row.segment_name),
      customerCount: parseInt(String(row.customer_count)),
      totalRevenue: parseFloat(String(row.total_revenue)),
      retentionRates: row.retention_rates as Record<string, number>,
      avgOrderValue: parseFloat(String(row.avg_order_value)),
      churnProbability: parseFloat(String(row.avg_churn_probability)),
    }));
  }

  /**
   * Calculate retention curves for visualization
   */
  private async calculateRetentionCurves(options: CohortAnalysisOptions): Promise<Record<string, RetentionData[]>> {
    const granularityTrunc = this.getDateTruncFunction(options.granularity);
    
    const retentionQuery = `
      WITH cohort_periods AS (
        SELECT DISTINCT ${granularityTrunc}acquisition_date) as cohort_period
        FROM customers 
        WHERE tenant_id = $1 
          AND acquisition_date >= $2::timestamptz 
          AND acquisition_date <= $3::timestamptz
      ),
      period_numbers AS (
        SELECT generate_series(0, 12) as period_number
      ),
      cohort_retention AS (
        SELECT 
          cp.cohort_period,
          pn.period_number,
          COUNT(DISTINCT c.id) as cohort_size,
          COUNT(DISTINCT CASE 
            WHEN o.created_at >= cp.cohort_period + (pn.period_number || ' months')::interval
              AND o.created_at < cp.cohort_period + ((pn.period_number + 1) || ' months')::interval
            THEN o.customer_id 
          END) as active_customers,
          COALESCE(SUM(CASE 
            WHEN o.created_at >= cp.cohort_period + (pn.period_number || ' months')::interval
              AND o.created_at < cp.cohort_period + ((pn.period_number + 1) || ' months')::interval
            THEN o.total_amount 
          END), 0) as period_revenue
        FROM cohort_periods cp
        CROSS JOIN period_numbers pn
        LEFT JOIN customers c ON ${granularityTrunc}c.acquisition_date) = cp.cohort_period
          AND c.tenant_id = $1
        LEFT JOIN orders o ON c.id::text = o.customer_id
        GROUP BY cp.cohort_period, pn.period_number
        HAVING COUNT(DISTINCT c.id) > 0
      )
      SELECT 
        cohort_period::text,
        period_number,
        cohort_size,
        active_customers,
        CASE
          WHEN cohort_size > 0 THEN ROUND(((active_customers::float / cohort_size) * 100)::numeric, 2)
          ELSE 0
        END as retention_rate,
        period_revenue
      FROM cohort_retention
      ORDER BY cohort_period, period_number
    `;

    const result = await query(retentionQuery, [options.tenantId, options.dateFrom, options.dateTo], options.tenantId);
    
    // Group by cohort period
    const curves: Record<string, RetentionData[]> = {};
    
    for (const row of result as Record<string, unknown>[]) {
      const cohortKey = String(row.cohort_period);
      if (!curves[cohortKey]) {
        curves[cohortKey] = [];
      }

      curves[cohortKey].push({
        period: parseInt(String(row.period_number)),
        retentionRate: parseFloat(String(row.retention_rate)),
        customerCount: parseInt(String(row.active_customers)),
        revenue: parseFloat(String(row.period_revenue)),
      });
    }
    
    return curves;
  }

  /**
   * Generate overview metrics for cohort analysis
   */
  private generateOverviewMetrics(
    _options: CohortAnalysisOptions,
    segments: CohortSegment[]
  ): CohortAnalysisResult['overview'] {
    const totalCohorts = new Set(segments.map(s => s.cohortMonth)).size;
    const totalCustomers = segments.reduce((sum, s) => sum + s.customerCount, 0);
    const totalRevenue = segments.reduce((sum, s) => sum + s.totalRevenue, 0);
    
    // Calculate average retention rate across all cohorts
    const allRetentionRates = segments.flatMap(s => Object.values(s.retentionRates));
    const avgRetentionRate = allRetentionRates.length > 0 
      ? allRetentionRates.reduce((sum, rate) => sum + rate, 0) / allRetentionRates.length 
      : 0;
    
    // Find best performing cohort by total revenue
    const bestPerformingCohort = segments.reduce((best, current) => 
      current.totalRevenue > best.totalRevenue ? current : best, 
      segments[0] || { cohortMonth: 'N/A', totalRevenue: 0 }
    ).cohortMonth;

    return {
      totalCohorts,
      avgRetentionRate: Math.round(avgRetentionRate * 100) / 100,
      bestPerformingCohort,
      totalCustomers,
      totalRevenue,
    };
  }

  /**
   * Generate predictive insights using historical cohort data
   */
  private generatePredictiveInsights(
    _options: CohortAnalysisOptions,
    segments: CohortSegment[]
  ): CohortAnalysisResult['predictiveInsights'] {
    // Calculate expected churn based on average churn probability
    const expectedChurn = segments.length > 0 
      ? segments.reduce((sum, s) => sum + s.churnProbability, 0) / segments.length 
      : 0;
    
    // Project revenue based on current trends
    const recentSegments = segments.slice(0, 3); // Last 3 cohorts
    const avgRevenuePerCustomer = recentSegments.length > 0
      ? recentSegments.reduce((sum, s) => sum + (s.totalRevenue / s.customerCount), 0) / recentSegments.length
      : 0;
    
    const revenueProjection = avgRevenuePerCustomer * (1 - expectedChurn) * 
      segments.reduce((sum, s) => sum + s.customerCount, 0);
    
    // Determine retention trend
    const retentionTrend = this.calculateRetentionTrend(segments);
    
    return {
      expectedChurn: Math.round(expectedChurn * 10000) / 100, // Convert to percentage
      revenueProjection: Math.round(revenueProjection * 100) / 100,
      retentionTrend,
    };
  }

  /**
   * Calculate retention trend based on cohort performance
   */
  private calculateRetentionTrend(segments: CohortSegment[]): 'improving' | 'declining' | 'stable' {
    if (segments.length < 2) return 'stable';
    
    // Sort by cohort month and compare recent vs older cohorts
    const sortedSegments = segments.sort((a, b) => a.cohortMonth.localeCompare(b.cohortMonth));
    const recentCohorts = sortedSegments.slice(-3);
    const olderCohorts = sortedSegments.slice(0, -3);
    
    if (olderCohorts.length === 0) return 'stable';
    
    const recentAvgRetention = recentCohorts.reduce((sum, s) => 
      sum + Object.values(s.retentionRates).reduce((a, b) => a + b, 0), 0
    ) / (recentCohorts.length * 3); // 3 retention periods
    
    const olderAvgRetention = olderCohorts.reduce((sum, s) => 
      sum + Object.values(s.retentionRates).reduce((a, b) => a + b, 0), 0
    ) / (olderCohorts.length * 3);
    
    const difference = recentAvgRetention - olderAvgRetention;
    
    if (difference > 2) return 'improving';
    if (difference < -2) return 'declining';
    return 'stable';
  }

  /**
   * Get appropriate date truncation function based on granularity
   */
  private getDateTruncFunction(granularity: string): string {
    switch (granularity) {
      case 'daily': return 'DATE_TRUNC(\'day\', ';
      case 'weekly': return 'DATE_TRUNC(\'week\', ';
      case 'monthly': return 'DATE_TRUNC(\'month\', ';
      default: return 'DATE_TRUNC(\'month\', ';
    }
  }
}
