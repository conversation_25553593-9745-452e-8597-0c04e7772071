import { Context, Middleware } from "@oak/oak";
import { config } from "../config/config.ts";
import { getRedisClient } from "../utils/redis.ts";
import { logger } from "../utils/logger.ts";
import { errors } from "./errorHandler.ts";

// Use a flexible type for Redis client to avoid strict typing issues
type FlexibleRedisClient = {
  multi(): any;
  get(key: string): Promise<string | null>;
  ttl(key: string): Promise<number>;
};

interface RateLimitOptions {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (ctx: Context) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

/**
 * Create a rate limiter middleware
 */
export function createRateLimiter(options: RateLimitOptions): Middleware {
  const {
    windowMs,
    maxRequests,
    keyGenerator = defaultKeyGenerator,
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
  } = options;

  return async (ctx: Context, next) => {
    const key = keyGenerator(ctx);
    const redisKey = `rate_limit:${key}`;

    try {
      const redis = getRedisClient();

      // Get current count
      const current = await redis.get(redisKey);
      const count = current ? parseInt(current) : 0;

      // Check if limit exceeded
      if (count >= maxRequests) {
        // Get TTL to provide retry-after header
        const ttl = await redis.ttl(redisKey);

        logger.warn("Rate limit exceeded", {
          key,
          count,
          limit: maxRequests,
          windowMs,
          ttl,
          ip: getClientIP(ctx),
          userAgent: ctx.request.headers.get("user-agent"),
        });

        ctx.response.headers.set("X-RateLimit-Limit", maxRequests.toString());
        ctx.response.headers.set("X-RateLimit-Remaining", "0");
        ctx.response.headers.set("X-RateLimit-Reset", (Date.now() + (ttl * 1000)).toString());
        ctx.response.headers.set("Retry-After", Math.max(ttl, 1).toString());

        throw errors.tooManyRequests("Rate limit exceeded. Please try again later.");
      }

      // Process request
      await next();

      // Increment counter after successful request (if not skipping)
      const shouldCount = !skipSuccessfulRequests ||
                         (skipFailedRequests && ctx.response.status >= 400);

      if (shouldCount) {
        await incrementCounter(redis, redisKey, windowMs);
      }

      // Add rate limit headers
      const newCount = count + (shouldCount ? 1 : 0);
      ctx.response.headers.set("X-RateLimit-Limit", maxRequests.toString());
      ctx.response.headers.set("X-RateLimit-Remaining", Math.max(0, maxRequests - newCount).toString());
      ctx.response.headers.set("X-RateLimit-Reset", (Date.now() + windowMs).toString());

    } catch (error) {
      // If Redis is unavailable, log warning but don't block requests
      const err = error as { message?: string };
      if (err.message?.includes("Rate limit exceeded")) {
        throw error;
      }

      logger.warn("Rate limiter Redis error, allowing request", {
        key,
        error: err.message,
      });

      // Only call next() if we haven't already processed the request
      if (!ctx.response.body) {
        await next();
      }
    }
  };
}

/**
 * Increment counter with expiration
 */
async function incrementCounter(redis: FlexibleRedisClient, key: string, windowMs: number): Promise<void> {
  const multi = redis.multi();
  multi.incr(key);
  multi.expire(key, Math.ceil(windowMs / 1000));
  await multi.exec();
}

/**
 * Default key generator based on IP address
 */
function defaultKeyGenerator(ctx: Context): string {
  const ip = getClientIP(ctx);
  const tenantId = ctx.request.headers.get("x-tenant-id");
  const userId = ctx.state.user?.id;
  
  // Use more specific key if available
  if (userId) {
    return `user:${userId}`;
  } else if (tenantId) {
    return `tenant:${tenantId}:ip:${ip}`;
  } else {
    return `ip:${ip}`;
  }
}

/**
 * Get client IP address
 */
function getClientIP(ctx: Context): string {
  return ctx.request.headers.get("x-forwarded-for")?.split(",")[0]?.trim() ||
         ctx.request.headers.get("x-real-ip") ||
         ctx.request.headers.get("cf-connecting-ip") ||
         "unknown";
}

/**
 * Create endpoint-specific rate limiter
 */
export function createEndpointRateLimiter(
  endpoint: string,
  maxRequests: number,
  windowMs: number = config.rateLimit.windowMs,
): Middleware {
  return createRateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (ctx) => {
      const baseKey = defaultKeyGenerator(ctx);
      return `${baseKey}:endpoint:${endpoint}`;
    },
  });
}

/**
 * Default rate limiter using global configuration
 */
export const rateLimiter = createRateLimiter({
  windowMs: config.rateLimit.windowMs,
  maxRequests: config.rateLimit.maxRequests,
});

/**
 * Strict rate limiter for sensitive endpoints
 */
export const strictRateLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 10,
});

/**
 * Lenient rate limiter for public endpoints
 */
export const lenientRateLimiter = createRateLimiter({
  windowMs: config.rateLimit.windowMs,
  maxRequests: config.rateLimit.maxRequests * 2,
});

/**
 * WebSocket connection rate limiter
 */
export const websocketRateLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 5, // 5 connections per minute
  keyGenerator: (ctx) => `websocket:${getClientIP(ctx)}`,
});
