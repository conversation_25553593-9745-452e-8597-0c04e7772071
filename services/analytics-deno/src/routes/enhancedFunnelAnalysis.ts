// Enhanced Funnel Analysis API Routes - Phase 2 Implementation
// Week 13-14: Advanced funnel analysis endpoints with conversion optimization
// Follows established pattern from cohort analysis and CLV API structure

import { Router } from "@oak/oak";
import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";
import { FunnelAnalysisService } from "../services/funnelAnalysisService.ts";
import { authMiddleware } from "../middleware/auth.ts";
import { bypassTenantValidation } from "../middleware/tenant.ts";

const router = new Router();
const funnelService = new FunnelAnalysisService();

// Validation schemas following established patterns
const FunnelAnalysisQuerySchema = z.object({
  tenant_id: z.string().uuid("Invalid tenant ID format"),
  funnel_id: z.string().uuid("Invalid funnel ID format").optional(),
  date_from: z.string().datetime("Invalid date_from format"),
  date_to: z.string().datetime("Invalid date_to format"),
  include_steps: z.string().transform(val => val === 'true').default('true'),
  include_events: z.string().transform(val => val === 'true').default('false'),
  include_analytics: z.string().transform(val => val === 'true').default('true'),
  granularity: z.enum(['hourly', 'daily', 'weekly', 'monthly']).default('daily'),
  customer_segments: z.string().optional(),
  traffic_sources: z.string().optional(),
  device_types: z.string().optional(),
  conversion_window_hours: z.string().transform(val => val ? parseInt(val) : undefined).optional(),
});

/**
 * GET /api/enhanced-analytics/funnels/analysis
 * Comprehensive funnel analysis with conversion optimization insights
 */
router.get("/analysis", authMiddleware, bypassTenantValidation, async (ctx) => {
  try {
    const startTime = performance.now();
    
    // Validate query parameters
    const queryParams = FunnelAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      funnel_id: ctx.request.url.searchParams.get("funnel_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      include_steps: ctx.request.url.searchParams.get("include_steps"),
      include_events: ctx.request.url.searchParams.get("include_events"),
      include_analytics: ctx.request.url.searchParams.get("include_analytics"),
      granularity: ctx.request.url.searchParams.get("granularity"),
      customer_segments: ctx.request.url.searchParams.get("customer_segments"),
      traffic_sources: ctx.request.url.searchParams.get("traffic_sources"),
      device_types: ctx.request.url.searchParams.get("device_types"),
      conversion_window_hours: ctx.request.url.searchParams.get("conversion_window_hours"),
    });

    // Build filter arrays from query parameters
    const customerSegments = queryParams.customer_segments ? queryParams.customer_segments.split(',') : undefined;
    const trafficSources = queryParams.traffic_sources ? queryParams.traffic_sources.split(',') : undefined;
    const deviceTypes = queryParams.device_types ? queryParams.device_types.split(',') : undefined;

    // Execute funnel analysis
    const funnelAnalysis = await funnelService.analyzeFunnel({
      tenantId: queryParams.tenant_id,
      funnelId: queryParams.funnel_id,
      dateFrom: queryParams.date_from,
      dateTo: queryParams.date_to,
      includeSteps: queryParams.include_steps,
      includeEvents: queryParams.include_events,
      includeAnalytics: queryParams.include_analytics,
      granularity: queryParams.granularity,
      customerSegments,
      trafficSources,
      deviceTypes,
      conversionWindowHours: queryParams.conversion_window_hours,
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: funnelAnalysis,
      metadata: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        funnelName: funnelAnalysis.funnel.funnelName,
        totalParticipants: funnelAnalysis.analytics.totalParticipants,
        overallConversionRate: funnelAnalysis.analytics.overallConversionRate,
        primaryBottleneck: funnelAnalysis.insights.primaryBottleneck,
        granularity: queryParams.granularity,
        generatedAt: funnelAnalysis.generatedAt,
      },
    };
  } catch (error) {
    console.error("Funnel analysis error:", error);
    
    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Invalid query parameters",
        details: error.errors,
      };
    } else {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: "Internal server error during funnel analysis",
        message: error.message,
      };
    }
  }
});

/**
 * GET /api/enhanced-analytics/funnels/steps
 * Individual step performance analysis
 */
router.get("/steps", authMiddleware, bypassTenantValidation, async (ctx) => {
  try {
    const startTime = performance.now();
    
    const queryParams = FunnelAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      funnel_id: ctx.request.url.searchParams.get("funnel_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      include_steps: ctx.request.url.searchParams.get("include_steps"),
      include_events: ctx.request.url.searchParams.get("include_events"),
      include_analytics: ctx.request.url.searchParams.get("include_analytics"),
      granularity: ctx.request.url.searchParams.get("granularity"),
    });

    const funnelAnalysis = await funnelService.analyzeFunnel({
      tenantId: queryParams.tenant_id,
      funnelId: queryParams.funnel_id,
      dateFrom: queryParams.date_from,
      dateTo: queryParams.date_to,
      includeSteps: true, // Always include steps for this endpoint
      includeEvents: false, // Only steps needed
      includeAnalytics: false,
      granularity: queryParams.granularity,
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: {
        funnel: {
          id: funnelAnalysis.funnel.id,
          name: funnelAnalysis.funnel.funnelName,
          type: funnelAnalysis.funnel.funnelType,
        },
        steps: funnelAnalysis.steps,
        stepInsights: {
          bottleneckStep: funnelAnalysis.steps.reduce((max, step) => 
            step.dropOffRate > max.dropOffRate ? step : max, funnelAnalysis.steps[0]
          ),
          bestPerformingStep: funnelAnalysis.steps.reduce((max, step) => 
            step.completionRate > max.completionRate ? step : max, funnelAnalysis.steps[0]
          ),
          avgCompletionRate: funnelAnalysis.steps.reduce((sum, step) => 
            sum + step.completionRate, 0) / funnelAnalysis.steps.length,
        },
      },
      metadata: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        totalSteps: funnelAnalysis.steps.length,
        funnelName: funnelAnalysis.funnel.funnelName,
        generatedAt: funnelAnalysis.generatedAt,
      },
    };
  } catch (error) {
    console.error("Funnel steps analysis error:", error);
    
    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Invalid query parameters",
        details: error.errors,
      };
    } else {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: "Internal server error during funnel steps analysis",
        message: error.message,
      };
    }
  }
});

/**
 * GET /api/enhanced-analytics/funnels/conversions
 * Conversion tracking and rates analysis
 */
router.get("/conversions", authMiddleware, bypassTenantValidation, async (ctx) => {
  try {
    const startTime = performance.now();
    
    const queryParams = FunnelAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      funnel_id: ctx.request.url.searchParams.get("funnel_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      include_steps: ctx.request.url.searchParams.get("include_steps"),
      include_events: ctx.request.url.searchParams.get("include_events"),
      include_analytics: ctx.request.url.searchParams.get("include_analytics"),
      granularity: ctx.request.url.searchParams.get("granularity"),
      customer_segments: ctx.request.url.searchParams.get("customer_segments"),
      traffic_sources: ctx.request.url.searchParams.get("traffic_sources"),
      device_types: ctx.request.url.searchParams.get("device_types"),
    });

    // Build filter arrays
    const customerSegments = queryParams.customer_segments ? queryParams.customer_segments.split(',') : undefined;
    const trafficSources = queryParams.traffic_sources ? queryParams.traffic_sources.split(',') : undefined;
    const deviceTypes = queryParams.device_types ? queryParams.device_types.split(',') : undefined;

    const funnelAnalysis = await funnelService.analyzeFunnel({
      tenantId: queryParams.tenant_id,
      funnelId: queryParams.funnel_id,
      dateFrom: queryParams.date_from,
      dateTo: queryParams.date_to,
      includeSteps: true,
      includeEvents: true, // Include events for conversion tracking
      includeAnalytics: true,
      granularity: queryParams.granularity,
      customerSegments,
      trafficSources,
      deviceTypes,
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: {
        conversionMetrics: {
          totalParticipants: funnelAnalysis.analytics.totalParticipants,
          totalConversions: funnelAnalysis.analytics.totalConversions,
          overallConversionRate: funnelAnalysis.analytics.overallConversionRate,
          avgTimeToConvert: funnelAnalysis.analytics.avgTimeToConvertSeconds,
          medianTimeToConvert: funnelAnalysis.analytics.medianTimeToConvertSeconds,
          totalRevenue: funnelAnalysis.analytics.totalRevenue,
          avgRevenuePerConversion: funnelAnalysis.analytics.avgRevenuePerConversion,
        },
        segmentPerformance: funnelAnalysis.analytics.segmentPerformance,
        trafficSourcePerformance: funnelAnalysis.analytics.trafficSourcePerformance,
        devicePerformance: funnelAnalysis.analytics.devicePerformance,
        conversionPaths: funnelAnalysis.analytics.conversionPaths,
        recentConversions: funnelAnalysis.conversionEvents.filter(event => 
          event.stepCompletionStatus === 'completed'
        ).slice(0, 20),
      },
      metadata: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        conversionRate: funnelAnalysis.analytics.overallConversionRate,
        totalRevenue: funnelAnalysis.analytics.totalRevenue,
        sampleSize: funnelAnalysis.analytics.totalParticipants,
        generatedAt: funnelAnalysis.generatedAt,
      },
    };
  } catch (error) {
    console.error("Funnel conversions analysis error:", error);
    
    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Invalid query parameters",
        details: error.errors,
      };
    } else {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: "Internal server error during funnel conversions analysis",
        message: error.message,
      };
    }
  }
});

/**
 * GET /api/enhanced-analytics/funnels/insights
 * Business recommendations for funnel optimization
 */
router.get("/insights", authMiddleware, bypassTenantValidation, async (ctx) => {
  try {
    const startTime = performance.now();
    
    const queryParams = FunnelAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      funnel_id: ctx.request.url.searchParams.get("funnel_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      include_steps: ctx.request.url.searchParams.get("include_steps"),
      include_events: ctx.request.url.searchParams.get("include_events"),
      include_analytics: ctx.request.url.searchParams.get("include_analytics"),
      granularity: ctx.request.url.searchParams.get("granularity"),
    });

    const funnelAnalysis = await funnelService.analyzeFunnel({
      tenantId: queryParams.tenant_id,
      funnelId: queryParams.funnel_id,
      dateFrom: queryParams.date_from,
      dateTo: queryParams.date_to,
      includeSteps: true,
      includeEvents: false,
      includeAnalytics: true,
      granularity: queryParams.granularity,
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: {
        insights: funnelAnalysis.insights,
        performance: funnelAnalysis.performance,
        optimizationPriority: {
          highPriority: funnelAnalysis.analytics.bottleneckSteps,
          mediumPriority: funnelAnalysis.analytics.optimizationOpportunities.slice(0, 3),
          lowPriority: funnelAnalysis.analytics.optimizationOpportunities.slice(3),
        },
        benchmarkComparison: funnelAnalysis.insights.benchmarkComparison,
      },
      metadata: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        primaryBottleneck: funnelAnalysis.insights.primaryBottleneck,
        conversionRate: funnelAnalysis.analytics.overallConversionRate,
        confidenceLevel: funnelAnalysis.performance.confidenceLevel,
        sampleSize: funnelAnalysis.performance.sampleSize,
        generatedAt: funnelAnalysis.generatedAt,
      },
    };
  } catch (error) {
    console.error("Funnel insights error:", error);
    
    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Invalid query parameters",
        details: error.errors,
      };
    } else {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: "Internal server error during funnel insights generation",
        message: error.message,
      };
    }
  }
});

/**
 * GET /api/enhanced-analytics/funnels/health
 * Health check for funnel analysis service
 */
router.get("/health", async (ctx) => {
  try {
    const startTime = performance.now();
    
    // Quick health check with minimal data
    const healthCheck = await funnelService.analyzeFunnel({
      tenantId: "00000000-0000-0000-0000-000000000000", // Test tenant
      dateFrom: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      dateTo: new Date().toISOString(),
      includeSteps: false,
      includeEvents: false,
      includeAnalytics: false,
      granularity: 'daily',
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      status: "healthy",
      service: "Funnel Analysis Service",
      version: "2.0.0",
      performance: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        targetMs: 500,
        status: executionTime < 500 ? "optimal" : "degraded",
      },
      capabilities: {
        granularities: ['hourly', 'daily', 'weekly', 'monthly'],
        funnelTypes: ['conversion', 'engagement', 'retention', 'custom'],
        analysisTypes: ['steps', 'conversions', 'insights', 'analytics'],
        filterSupport: true,
        realTimeTracking: true,
        multiTenantSupport: true,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Funnel health check error:", error);
    
    ctx.response.status = 503;
    ctx.response.body = {
      success: false,
      status: "unhealthy",
      service: "Funnel Analysis Service",
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
});

export default router;
