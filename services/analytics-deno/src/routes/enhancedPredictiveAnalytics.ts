// Enhanced Predictive Analytics Routes - Phase 2 Week 15-16
// RESTful API endpoints for ML predictions, churn analysis, revenue forecasting, and anomaly detection
// Follows established patterns from cohort analysis, CLV calculation, and funnel analysis systems

import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { PredictiveAnalyticsService } from "../services/predictiveAnalyticsService.ts";
import { validateTenantAccess } from "../middleware/auth.ts";
import { rateLimitMiddleware } from "../middleware/rateLimit.ts";

// =====================================================
// VALIDATION SCHEMAS
// =====================================================

// General prediction request schema
const predictionRequestSchema = z.object({
  tenantId: z.string().uuid(),
  modelType: z.enum(['churn_prediction', 'revenue_forecasting', 'behavior_prediction', 'anomaly_detection']),
  modelId: z.string().uuid().optional(),
  entityId: z.string().uuid().optional(),
  entityType: z.string().optional(),
  predictionHorizonDays: z.number().int().min(1).max(365).optional(),
  includeConfidence: z.boolean().optional(),
  includeExplanation: z.boolean().optional()
});

// Churn prediction request schema
const churnPredictionSchema = z.object({
  tenantId: z.string().uuid(),
  customerId: z.string().uuid().optional(),
  includeRiskFactors: z.boolean().optional(),
  includeRecommendations: z.boolean().optional(),
  riskThreshold: z.number().min(0).max(1).optional()
});

// Revenue forecast request schema
const revenueForecastSchema = z.object({
  tenantId: z.string().uuid(),
  forecastType: z.enum(['daily', 'weekly', 'monthly', 'quarterly']),
  forecastHorizonDays: z.number().int().min(1).max(365),
  includeConfidenceInterval: z.boolean().optional(),
  includeComponents: z.boolean().optional(),
  algorithm: z.enum(['arima', 'prophet', 'lstm', 'ensemble']).optional()
});

// Behavior prediction request schema
const behaviorPredictionSchema = z.object({
  tenantId: z.string().uuid(),
  customerId: z.string().uuid(),
  behaviorTypes: z.array(z.string()).optional(),
  includeRecommendations: z.boolean().optional(),
  includeTimingPredictions: z.boolean().optional()
});

// Anomaly detection request schema
const anomalyDetectionSchema = z.object({
  tenantId: z.string().uuid(),
  anomalyTypes: z.array(z.string()).optional(),
  severityThreshold: z.number().min(0).max(1).optional(),
  timeWindowHours: z.number().int().min(1).max(168).optional(), // Max 1 week
  includeContext: z.boolean().optional()
});

// Batch prediction request schema
const batchPredictionSchema = z.object({
  tenantId: z.string().uuid(),
  modelType: z.string(),
  entityIds: z.array(z.string().uuid()).min(1).max(1000), // Max 1000 entities per batch
  predictionHorizonDays: z.number().int().min(1).max(365).optional(),
  includeConfidence: z.boolean().optional()
});

// =====================================================
// ROUTER SETUP
// =====================================================

export const enhancedPredictiveAnalyticsRouter = new Router();

// Apply middleware
enhancedPredictiveAnalyticsRouter.use(rateLimitMiddleware);
enhancedPredictiveAnalyticsRouter.use(validateTenantAccess);

// Initialize service
const predictiveAnalyticsService = new PredictiveAnalyticsService();

// =====================================================
// GENERAL PREDICTION ENDPOINTS
// =====================================================

/**
 * POST /api/enhanced-analytics/predictions/generate
 * Generate ML predictions using specified model
 */
enhancedPredictiveAnalyticsRouter.post("/predictions/generate", async (ctx) => {
  const startTime = performance.now();
  
  try {
    // Validate request body
    const body = await ctx.request.body().value;
    const validatedRequest = predictionRequestSchema.parse(body);

    logger.info("Generating ML prediction", {
      tenantId: validatedRequest.tenantId,
      modelType: validatedRequest.modelType,
      entityId: validatedRequest.entityId
    });

    // Generate prediction
    const prediction = await predictiveAnalyticsService.generatePrediction(validatedRequest);

    const executionTime = performance.now() - startTime;
    
    ctx.response.body = {
      success: true,
      data: prediction,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        modelType: validatedRequest.modelType,
        confidenceScore: prediction.confidenceScore
      },
      timestamp: new Date().toISOString()
    };

    logger.info("ML prediction generated successfully", {
      tenantId: validatedRequest.tenantId,
      predictionId: prediction.predictionId,
      executionTime: `${executionTime.toFixed(2)}ms`
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to generate ML prediction", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = error instanceof z.ZodError ? 400 : 500;
    ctx.response.body = {
      success: false,
      error: error instanceof z.ZodError 
        ? "Invalid request parameters"
        : "Failed to generate prediction",
      details: error instanceof z.ZodError ? error.errors : undefined,
      timestamp: new Date().toISOString()
    };
  }
});

/**
 * POST /api/enhanced-analytics/predictions/batch
 * Generate batch predictions for multiple entities
 */
enhancedPredictiveAnalyticsRouter.post("/predictions/batch", async (ctx) => {
  const startTime = performance.now();
  
  try {
    // Validate request body
    const body = await ctx.request.body().value;
    const validatedRequest = batchPredictionSchema.parse(body);

    logger.info("Generating batch ML predictions", {
      tenantId: validatedRequest.tenantId,
      modelType: validatedRequest.modelType,
      entityCount: validatedRequest.entityIds.length
    });

    // Generate batch predictions
    const predictions = await predictiveAnalyticsService.generateBatchPredictions(
      validatedRequest.tenantId,
      validatedRequest.modelType,
      validatedRequest.entityIds,
      {
        predictionHorizonDays: validatedRequest.predictionHorizonDays,
        includeConfidence: validatedRequest.includeConfidence
      }
    );

    const executionTime = performance.now() - startTime;
    const avgConfidence = predictions.reduce((sum, p) => sum + p.confidenceScore, 0) / predictions.length;
    
    ctx.response.body = {
      success: true,
      data: predictions,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        modelType: validatedRequest.modelType,
        entityCount: predictions.length,
        avgConfidence: avgConfidence.toFixed(4)
      },
      timestamp: new Date().toISOString()
    };

    logger.info("Batch ML predictions completed", {
      tenantId: validatedRequest.tenantId,
      entityCount: predictions.length,
      executionTime: `${executionTime.toFixed(2)}ms`,
      avgConfidence
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to generate batch ML predictions", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = error instanceof z.ZodError ? 400 : 500;
    ctx.response.body = {
      success: false,
      error: error instanceof z.ZodError 
        ? "Invalid request parameters"
        : "Failed to generate batch predictions",
      details: error instanceof z.ZodError ? error.errors : undefined,
      timestamp: new Date().toISOString()
    };
  }
});

// =====================================================
// CHURN PREDICTION ENDPOINTS
// =====================================================

/**
 * GET /api/enhanced-analytics/predictions/churn
 * Predict customer churn risk with detailed analysis
 */
enhancedPredictiveAnalyticsRouter.get("/predictions/churn", async (ctx) => {
  const startTime = performance.now();
  
  try {
    // Validate query parameters
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = churnPredictionSchema.parse(queryParams);

    logger.info("Predicting customer churn", {
      tenantId: validatedQuery.tenantId,
      customerId: validatedQuery.customerId
    });

    // Predict churn
    const churnPredictions = await predictiveAnalyticsService.predictCustomerChurn(validatedQuery);

    const executionTime = performance.now() - startTime;
    const highRiskCustomers = churnPredictions.filter(p => p.riskLevel === 'high' || p.riskLevel === 'critical').length;
    
    ctx.response.body = {
      success: true,
      data: churnPredictions,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        customersAnalyzed: churnPredictions.length,
        highRiskCustomers,
        avgChurnProbability: churnPredictions.reduce((sum, p) => sum + p.churnProbability, 0) / churnPredictions.length
      },
      timestamp: new Date().toISOString()
    };

    logger.info("Customer churn prediction completed", {
      tenantId: validatedQuery.tenantId,
      customersAnalyzed: churnPredictions.length,
      executionTime: `${executionTime.toFixed(2)}ms`,
      highRiskCustomers
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to predict customer churn", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = error instanceof z.ZodError ? 400 : 500;
    ctx.response.body = {
      success: false,
      error: error instanceof z.ZodError 
        ? "Invalid query parameters"
        : "Failed to predict customer churn",
      details: error instanceof z.ZodError ? error.errors : undefined,
      timestamp: new Date().toISOString()
    };
  }
});

/**
 * GET /api/enhanced-analytics/predictions/churn/summary
 * Get churn prediction summary and statistics
 */
enhancedPredictiveAnalyticsRouter.get("/predictions/churn/summary", async (ctx) => {
  const startTime = performance.now();
  
  try {
    const tenantId = ctx.request.url.searchParams.get("tenantId");
    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "tenantId parameter is required",
        timestamp: new Date().toISOString()
      };
      return;
    }

    logger.info("Getting churn prediction summary", { tenantId });

    // Get churn summary from continuous aggregate
    const churnSummary = await predictiveAnalyticsService.predictCustomerChurn({
      tenantId,
      includeRiskFactors: false
    });

    // Calculate summary statistics
    const totalCustomers = churnSummary.length;
    const riskDistribution = {
      critical: churnSummary.filter(p => p.riskLevel === 'critical').length,
      high: churnSummary.filter(p => p.riskLevel === 'high').length,
      medium: churnSummary.filter(p => p.riskLevel === 'medium').length,
      low: churnSummary.filter(p => p.riskLevel === 'low').length
    };

    const avgChurnProbability = totalCustomers > 0 
      ? churnSummary.reduce((sum, p) => sum + p.churnProbability, 0) / totalCustomers 
      : 0;

    const executionTime = performance.now() - startTime;
    
    ctx.response.body = {
      success: true,
      data: {
        totalCustomers,
        riskDistribution,
        avgChurnProbability: parseFloat(avgChurnProbability.toFixed(4)),
        highRiskCustomers: riskDistribution.critical + riskDistribution.high,
        interventionRequired: riskDistribution.critical + riskDistribution.high
      },
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    };

    logger.info("Churn prediction summary completed", {
      tenantId,
      totalCustomers,
      highRiskCustomers: riskDistribution.critical + riskDistribution.high,
      executionTime: `${executionTime.toFixed(2)}ms`
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to get churn prediction summary", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to get churn prediction summary",
      timestamp: new Date().toISOString()
    };
  }
});

// =====================================================
// REVENUE FORECASTING ENDPOINTS
// =====================================================

/**
 * GET /api/enhanced-analytics/predictions/revenue-forecast
 * Generate revenue forecasts with confidence intervals
 */
enhancedPredictiveAnalyticsRouter.get("/predictions/revenue-forecast", async (ctx) => {
  const startTime = performance.now();

  try {
    // Validate query parameters
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = revenueForecastSchema.parse(queryParams);

    logger.info("Generating revenue forecast", {
      tenantId: validatedQuery.tenantId,
      forecastType: validatedQuery.forecastType,
      forecastHorizonDays: validatedQuery.forecastHorizonDays
    });

    // Generate revenue forecast
    const forecasts = await predictiveAnalyticsService.generateRevenueForecast(validatedQuery);

    const executionTime = performance.now() - startTime;
    const totalForecastedRevenue = forecasts.reduce((sum, f) => sum + f.forecastedRevenue, 0);
    const avgAccuracy = forecasts.reduce((sum, f) => sum + (f.forecastAccuracy || 0), 0) / forecasts.length;

    ctx.response.body = {
      success: true,
      data: forecasts,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        forecastType: validatedQuery.forecastType,
        forecastPoints: forecasts.length,
        totalForecastedRevenue: parseFloat(totalForecastedRevenue.toFixed(2)),
        avgAccuracy: parseFloat(avgAccuracy.toFixed(4)),
        algorithm: validatedQuery.algorithm || 'ensemble'
      },
      timestamp: new Date().toISOString()
    };

    logger.info("Revenue forecast completed", {
      tenantId: validatedQuery.tenantId,
      forecastPoints: forecasts.length,
      executionTime: `${executionTime.toFixed(2)}ms`,
      totalForecastedRevenue
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to generate revenue forecast", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = error instanceof z.ZodError ? 400 : 500;
    ctx.response.body = {
      success: false,
      error: error instanceof z.ZodError
        ? "Invalid query parameters"
        : "Failed to generate revenue forecast",
      details: error instanceof z.ZodError ? error.errors : undefined,
      timestamp: new Date().toISOString()
    };
  }
});

/**
 * GET /api/enhanced-analytics/predictions/revenue-forecast/accuracy
 * Get revenue forecast accuracy metrics and validation
 */
enhancedPredictiveAnalyticsRouter.get("/predictions/revenue-forecast/accuracy", async (ctx) => {
  const startTime = performance.now();

  try {
    const tenantId = ctx.request.url.searchParams.get("tenantId");
    const forecastType = ctx.request.url.searchParams.get("forecastType") || "daily";

    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "tenantId parameter is required",
        timestamp: new Date().toISOString()
      };
      return;
    }

    logger.info("Getting revenue forecast accuracy", { tenantId, forecastType });

    // Generate sample forecast for accuracy analysis
    const forecasts = await predictiveAnalyticsService.generateRevenueForecast({
      tenantId,
      forecastType: forecastType as 'daily' | 'weekly' | 'monthly' | 'quarterly',
      forecastHorizonDays: 30,
      includeConfidenceInterval: true
    });

    // Calculate accuracy metrics
    const accuracyMetrics = {
      avgAccuracy: forecasts.reduce((sum, f) => sum + (f.forecastAccuracy || 0), 0) / forecasts.length,
      minAccuracy: Math.min(...forecasts.map(f => f.forecastAccuracy || 0)),
      maxAccuracy: Math.max(...forecasts.map(f => f.forecastAccuracy || 0)),
      forecastCount: forecasts.length,
      confidenceLevel: forecasts[0]?.confidenceLevel || 0.95
    };

    const executionTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: accuracyMetrics,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        forecastType,
        evaluationPeriod: "30 days"
      },
      timestamp: new Date().toISOString()
    };

    logger.info("Revenue forecast accuracy analysis completed", {
      tenantId,
      forecastType,
      avgAccuracy: accuracyMetrics.avgAccuracy,
      executionTime: `${executionTime.toFixed(2)}ms`
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to get revenue forecast accuracy", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to get revenue forecast accuracy",
      timestamp: new Date().toISOString()
    };
  }
});

// =====================================================
// BEHAVIOR PREDICTION ENDPOINTS
// =====================================================

/**
 * GET /api/enhanced-analytics/predictions/behavior
 * Predict customer behavior and next-best-actions
 */
enhancedPredictiveAnalyticsRouter.get("/predictions/behavior", async (ctx) => {
  const startTime = performance.now();

  try {
    // Validate query parameters
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = behaviorPredictionSchema.parse(queryParams);

    logger.info("Predicting customer behavior", {
      tenantId: validatedQuery.tenantId,
      customerId: validatedQuery.customerId,
      behaviorTypes: validatedQuery.behaviorTypes
    });

    // Predict behavior
    const behaviorPredictions = await predictiveAnalyticsService.predictCustomerBehavior(validatedQuery);

    const executionTime = performance.now() - startTime;
    const avgConfidence = behaviorPredictions.reduce((sum, p) => sum + p.confidenceScore, 0) / behaviorPredictions.length;
    const highPriorityActions = behaviorPredictions.filter(p => p.actionPriority >= 8).length;

    ctx.response.body = {
      success: true,
      data: behaviorPredictions,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        customerId: validatedQuery.customerId,
        predictionsGenerated: behaviorPredictions.length,
        avgConfidence: parseFloat(avgConfidence.toFixed(4)),
        highPriorityActions
      },
      timestamp: new Date().toISOString()
    };

    logger.info("Customer behavior prediction completed", {
      tenantId: validatedQuery.tenantId,
      customerId: validatedQuery.customerId,
      predictionsGenerated: behaviorPredictions.length,
      executionTime: `${executionTime.toFixed(2)}ms`,
      avgConfidence
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to predict customer behavior", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = error instanceof z.ZodError ? 400 : 500;
    ctx.response.body = {
      success: false,
      error: error instanceof z.ZodError
        ? "Invalid query parameters"
        : "Failed to predict customer behavior",
      details: error instanceof z.ZodError ? error.errors : undefined,
      timestamp: new Date().toISOString()
    };
  }
});

// =====================================================
// ANOMALY DETECTION ENDPOINTS
// =====================================================

/**
 * GET /api/enhanced-analytics/predictions/anomalies
 * Detect anomalies in real-time data streams
 */
enhancedPredictiveAnalyticsRouter.get("/predictions/anomalies", async (ctx) => {
  const startTime = performance.now();

  try {
    // Validate query parameters
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = anomalyDetectionSchema.parse(queryParams);

    logger.info("Detecting anomalies", {
      tenantId: validatedQuery.tenantId,
      anomalyTypes: validatedQuery.anomalyTypes,
      timeWindowHours: validatedQuery.timeWindowHours
    });

    // Detect anomalies
    const anomalies = await predictiveAnalyticsService.detectAnomalies(validatedQuery);

    const executionTime = performance.now() - startTime;
    const criticalAnomalies = anomalies.filter(a => a.severityLevel === 'critical').length;
    const avgAnomalyScore = anomalies.length > 0
      ? anomalies.reduce((sum, a) => sum + a.anomalyScore, 0) / anomalies.length
      : 0;

    ctx.response.body = {
      success: true,
      data: anomalies,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        anomaliesDetected: anomalies.length,
        criticalAnomalies,
        avgAnomalyScore: parseFloat(avgAnomalyScore.toFixed(4)),
        timeWindowHours: validatedQuery.timeWindowHours || 24
      },
      timestamp: new Date().toISOString()
    };

    logger.info("Anomaly detection completed", {
      tenantId: validatedQuery.tenantId,
      anomaliesDetected: anomalies.length,
      criticalAnomalies,
      executionTime: `${executionTime.toFixed(2)}ms`
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to detect anomalies", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = error instanceof z.ZodError ? 400 : 500;
    ctx.response.body = {
      success: false,
      error: error instanceof z.ZodError
        ? "Invalid query parameters"
        : "Failed to detect anomalies",
      details: error instanceof z.ZodError ? error.errors : undefined,
      timestamp: new Date().toISOString()
    };
  }
});

/**
 * GET /api/enhanced-analytics/predictions/anomalies/summary
 * Get anomaly detection summary and statistics
 */
enhancedPredictiveAnalyticsRouter.get("/predictions/anomalies/summary", async (ctx) => {
  const startTime = performance.now();

  try {
    const tenantId = ctx.request.url.searchParams.get("tenantId");
    const timeWindowHours = parseInt(ctx.request.url.searchParams.get("timeWindowHours") || "24");

    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "tenantId parameter is required",
        timestamp: new Date().toISOString()
      };
      return;
    }

    logger.info("Getting anomaly detection summary", { tenantId, timeWindowHours });

    // Get anomaly summary
    const anomalies = await predictiveAnalyticsService.detectAnomalies({
      tenantId,
      timeWindowHours,
      includeContext: false
    });

    // Calculate summary statistics
    const severityDistribution = {
      critical: anomalies.filter(a => a.severityLevel === 'critical').length,
      high: anomalies.filter(a => a.severityLevel === 'high').length,
      medium: anomalies.filter(a => a.severityLevel === 'medium').length,
      low: anomalies.filter(a => a.severityLevel === 'low').length
    };

    const typeDistribution = anomalies.reduce((acc, anomaly) => {
      acc[anomaly.anomalyType] = (acc[anomaly.anomalyType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const avgAnomalyScore = anomalies.length > 0
      ? anomalies.reduce((sum, a) => sum + a.anomalyScore, 0) / anomalies.length
      : 0;

    const executionTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: {
        totalAnomalies: anomalies.length,
        severityDistribution,
        typeDistribution,
        avgAnomalyScore: parseFloat(avgAnomalyScore.toFixed(4)),
        criticalAnomalies: severityDistribution.critical,
        requiresAttention: severityDistribution.critical + severityDistribution.high
      },
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        timeWindowHours,
        generatedAt: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    };

    logger.info("Anomaly detection summary completed", {
      tenantId,
      totalAnomalies: anomalies.length,
      criticalAnomalies: severityDistribution.critical,
      executionTime: `${executionTime.toFixed(2)}ms`
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to get anomaly detection summary", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to get anomaly detection summary",
      timestamp: new Date().toISOString()
    };
  }
});

// =====================================================
// MODEL MANAGEMENT ENDPOINTS
// =====================================================

/**
 * GET /api/enhanced-analytics/predictions/models
 * Get available ML models and their status
 */
enhancedPredictiveAnalyticsRouter.get("/predictions/models", (ctx) => {
  const startTime = performance.now();

  try {
    const tenantId = ctx.request.url.searchParams.get("tenantId");
    const modelType = ctx.request.url.searchParams.get("modelType");

    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "tenantId parameter is required",
        timestamp: new Date().toISOString()
      };
      return;
    }

    logger.info("Getting ML models", { tenantId, modelType });

    // This would typically query the ml_models table
    // For now, return mock data following the established pattern
    const models = [
      {
        id: "model_001",
        modelName: "churn_prediction_v1",
        modelType: "churn_prediction",
        algorithm: "random_forest",
        status: "active",
        isProduction: true,
        trainingAccuracy: 0.92,
        validationAccuracy: 0.89,
        createdAt: new Date().toISOString()
      },
      {
        id: "model_002",
        modelName: "revenue_forecast_v1",
        modelType: "revenue_forecasting",
        algorithm: "lstm",
        status: "active",
        isProduction: true,
        trainingAccuracy: 0.87,
        validationAccuracy: 0.85,
        createdAt: new Date().toISOString()
      }
    ];

    const filteredModels = modelType
      ? models.filter(m => m.modelType === modelType)
      : models;

    const executionTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: filteredModels,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        modelCount: filteredModels.length,
        activeModels: filteredModels.filter(m => m.status === 'active').length,
        productionModels: filteredModels.filter(m => m.isProduction).length
      },
      timestamp: new Date().toISOString()
    };

    logger.info("ML models retrieved", {
      tenantId,
      modelCount: filteredModels.length,
      executionTime: `${executionTime.toFixed(2)}ms`
    });

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Failed to get ML models", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to get ML models",
      timestamp: new Date().toISOString()
    };
  }
});

// =====================================================
// HEALTH CHECK ENDPOINT
// =====================================================

/**
 * GET /api/enhanced-analytics/predictions/health
 * Health check for predictive analytics service
 */
enhancedPredictiveAnalyticsRouter.get("/predictions/health", (ctx) => {
  const startTime = performance.now();

  try {
    // Basic health check
    const health = {
      status: "healthy",
      service: "predictive-analytics",
      version: "1.0.0",
      uptime: Date.now(), // Use Date.now() instead of process.uptime() for Deno
      timestamp: new Date().toISOString()
    };

    const executionTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: health,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`
      }
    };

  } catch (error) {
    const executionTime = performance.now() - startTime;
    logger.error("Health check failed", {
      executionTime: `${executionTime.toFixed(2)}ms`,
      error: (error as Error).message
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Health check failed",
      timestamp: new Date().toISOString()
    };
  }
});

export default enhancedPredictiveAnalyticsRouter;
