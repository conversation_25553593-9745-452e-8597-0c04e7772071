// Enhanced CLV (Customer Lifetime Value) Analysis API Routes - Phase 2 Implementation
// Week 11-12: Advanced CLV prediction endpoints with ML integration
// Follows established pattern from cohort analysis API structure

import { Router } from "@oak/oak";
import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";
import { CLVCalculationService } from "../services/clvCalculationService.ts";
import { authMiddleware } from "../middleware/auth.ts";
import { bypassTenantValidation } from "../middleware/tenant.ts";

const router = new Router();
const clvService = new CLVCalculationService();

// Validation schemas following established patterns
const CLVAnalysisQuerySchema = z.object({
  tenant_id: z.string().uuid("Invalid tenant ID format"),
  date_from: z.string().datetime("Invalid date_from format"),
  date_to: z.string().datetime("Invalid date_to format"),
  model_type: z.enum(['traditional', 'ml_ensemble', 'deep_learning', 'auto']).default('auto'),
  include_segmentation: z.string().transform(val => val === 'true').default('true'),
  include_predictions: z.string().transform(val => val === 'true').default('true'),
  prediction_horizon: z.enum(['12m', '24m', 'lifetime']).default('lifetime'),
  customer_segments: z.string().optional(),
  min_clv: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  max_clv: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  churn_risk_threshold: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
});

/**
 * GET /api/enhanced-analytics/clv/analysis
 * Comprehensive CLV analysis with advanced predictions and segmentation
 */
router.get("/analysis", authMiddleware, bypassTenantValidation, async (ctx) => {
  try {
    const startTime = performance.now();
    
    // Validate query parameters
    const queryParams = CLVAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      model_type: ctx.request.url.searchParams.get("model_type"),
      include_segmentation: ctx.request.url.searchParams.get("include_segmentation"),
      include_predictions: ctx.request.url.searchParams.get("include_predictions"),
      prediction_horizon: ctx.request.url.searchParams.get("prediction_horizon"),
      customer_segments: ctx.request.url.searchParams.get("customer_segments"),
      min_clv: ctx.request.url.searchParams.get("min_clv"),
      max_clv: ctx.request.url.searchParams.get("max_clv"),
      churn_risk_threshold: ctx.request.url.searchParams.get("churn_risk_threshold"),
    });

    // Build customer filter from query parameters
    const customerFilter: {
      segments?: string[];
      minClv?: number;
      maxClv?: number;
      churnRiskThreshold?: number;
    } = {};

    if (queryParams.customer_segments) {
      customerFilter.segments = queryParams.customer_segments.split(',');
    }
    if (queryParams.min_clv !== undefined) {
      customerFilter.minClv = queryParams.min_clv;
    }
    if (queryParams.max_clv !== undefined) {
      customerFilter.maxClv = queryParams.max_clv;
    }
    if (queryParams.churn_risk_threshold !== undefined) {
      customerFilter.churnRiskThreshold = queryParams.churn_risk_threshold;
    }

    // Execute CLV analysis
    const clvAnalysis = await clvService.calculateCLV({
      tenantId: queryParams.tenant_id,
      dateFrom: queryParams.date_from,
      dateTo: queryParams.date_to,
      modelType: queryParams.model_type,
      includeSegmentation: queryParams.include_segmentation,
      includePredictions: queryParams.include_predictions,
      predictionHorizon: queryParams.prediction_horizon,
      customerFilter: Object.keys(customerFilter).length > 0 ? customerFilter : undefined,
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: clvAnalysis,
      metadata: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        totalCustomers: clvAnalysis.overview.totalCustomers,
        totalPredictedValue: clvAnalysis.overview.totalPredictedValue,
        modelType: queryParams.model_type,
        predictionHorizon: queryParams.prediction_horizon,
        generatedAt: clvAnalysis.generatedAt,
      },
    };
  } catch (error) {
    console.error("CLV analysis error:", error);
    
    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Invalid query parameters",
        details: error.errors,
      };
    } else {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: "Internal server error during CLV analysis",
        message: error.message,
      };
    }
  }
});

/**
 * GET /api/enhanced-analytics/clv/predictions
 * Get detailed CLV predictions for specific customers
 */
router.get("/predictions", authMiddleware, bypassTenantValidation, async (ctx) => {
  try {
    const startTime = performance.now();
    
    const queryParams = CLVAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      model_type: ctx.request.url.searchParams.get("model_type"),
      include_segmentation: ctx.request.url.searchParams.get("include_segmentation"),
      include_predictions: ctx.request.url.searchParams.get("include_predictions"),
      prediction_horizon: ctx.request.url.searchParams.get("prediction_horizon"),
      customer_segments: ctx.request.url.searchParams.get("customer_segments"),
      min_clv: ctx.request.url.searchParams.get("min_clv"),
      max_clv: ctx.request.url.searchParams.get("max_clv"),
      churn_risk_threshold: ctx.request.url.searchParams.get("churn_risk_threshold"),
    });

    // Build customer filter
    const customerFilter: {
      segments?: string[];
      minClv?: number;
      maxClv?: number;
      churnRiskThreshold?: number;
    } = {};

    if (queryParams.customer_segments) {
      customerFilter.segments = queryParams.customer_segments.split(',');
    }
    if (queryParams.min_clv !== undefined) {
      customerFilter.minClv = queryParams.min_clv;
    }
    if (queryParams.max_clv !== undefined) {
      customerFilter.maxClv = queryParams.max_clv;
    }
    if (queryParams.churn_risk_threshold !== undefined) {
      customerFilter.churnRiskThreshold = queryParams.churn_risk_threshold;
    }

    const clvAnalysis = await clvService.calculateCLV({
      tenantId: queryParams.tenant_id,
      dateFrom: queryParams.date_from,
      dateTo: queryParams.date_to,
      modelType: queryParams.model_type,
      includeSegmentation: false, // Only predictions needed
      includePredictions: true,
      predictionHorizon: queryParams.prediction_horizon,
      customerFilter: Object.keys(customerFilter).length > 0 ? customerFilter : undefined,
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: {
        predictions: clvAnalysis.predictions,
        modelPerformance: clvAnalysis.modelPerformance,
      },
      metadata: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        totalPredictions: clvAnalysis.predictions.length,
        modelType: queryParams.model_type,
        predictionHorizon: queryParams.prediction_horizon,
        avgConfidence: clvAnalysis.modelPerformance.confidence,
        generatedAt: clvAnalysis.generatedAt,
      },
    };
  } catch (error) {
    console.error("CLV predictions error:", error);
    
    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Invalid query parameters",
        details: error.errors,
      };
    } else {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: "Internal server error during CLV predictions",
        message: error.message,
      };
    }
  }
});

/**
 * GET /api/enhanced-analytics/clv/segments
 * Get CLV segments with advanced analytics
 */
router.get("/segments", authMiddleware, bypassTenantValidation, async (ctx) => {
  try {
    const startTime = performance.now();
    
    const queryParams = CLVAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      model_type: ctx.request.url.searchParams.get("model_type"),
      include_segmentation: ctx.request.url.searchParams.get("include_segmentation"),
      include_predictions: ctx.request.url.searchParams.get("include_predictions"),
      prediction_horizon: ctx.request.url.searchParams.get("prediction_horizon"),
    });

    const clvAnalysis = await clvService.calculateCLV({
      tenantId: queryParams.tenant_id,
      dateFrom: queryParams.date_from,
      dateTo: queryParams.date_to,
      modelType: queryParams.model_type,
      includeSegmentation: true,
      includePredictions: false, // Only segments needed
      predictionHorizon: queryParams.prediction_horizon,
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: {
        segments: clvAnalysis.segments,
        overview: clvAnalysis.overview,
        insights: clvAnalysis.insights,
      },
      metadata: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        totalSegments: clvAnalysis.segments.length,
        totalCustomers: clvAnalysis.overview.totalCustomers,
        totalValue: clvAnalysis.overview.totalPredictedValue,
        generatedAt: clvAnalysis.generatedAt,
      },
    };
  } catch (error) {
    console.error("CLV segments error:", error);
    
    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Invalid query parameters",
        details: error.errors,
      };
    } else {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: "Internal server error during CLV segments analysis",
        message: error.message,
      };
    }
  }
});

/**
 * GET /api/enhanced-analytics/clv/insights
 * Get business insights and recommendations based on CLV analysis
 */
router.get("/insights", authMiddleware, bypassTenantValidation, async (ctx) => {
  try {
    const startTime = performance.now();
    
    const queryParams = CLVAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      model_type: ctx.request.url.searchParams.get("model_type"),
      include_segmentation: ctx.request.url.searchParams.get("include_segmentation"),
      include_predictions: ctx.request.url.searchParams.get("include_predictions"),
      prediction_horizon: ctx.request.url.searchParams.get("prediction_horizon"),
    });

    const clvAnalysis = await clvService.calculateCLV({
      tenantId: queryParams.tenant_id,
      dateFrom: queryParams.date_from,
      dateTo: queryParams.date_to,
      modelType: queryParams.model_type,
      includeSegmentation: true,
      includePredictions: true,
      predictionHorizon: queryParams.prediction_horizon,
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: {
        insights: clvAnalysis.insights,
        overview: clvAnalysis.overview,
        modelPerformance: clvAnalysis.modelPerformance,
      },
      metadata: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        totalCustomers: clvAnalysis.overview.totalCustomers,
        highValueCustomers: clvAnalysis.overview.highValueCustomers,
        atRiskCustomers: clvAnalysis.overview.atRiskCustomers,
        modelAccuracy: clvAnalysis.overview.modelAccuracy,
        generatedAt: clvAnalysis.generatedAt,
      },
    };
  } catch (error) {
    console.error("CLV insights error:", error);
    
    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Invalid query parameters",
        details: error.errors,
      };
    } else {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: "Internal server error during CLV insights generation",
        message: error.message,
      };
    }
  }
});

/**
 * GET /api/enhanced-analytics/clv/health
 * Health check for CLV calculation service
 */
router.get("/health", async (ctx) => {
  try {
    const startTime = performance.now();
    
    // Quick health check with minimal data
    const healthCheck = await clvService.calculateCLV({
      tenantId: "00000000-0000-0000-0000-000000000000", // Test tenant
      dateFrom: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      dateTo: new Date().toISOString(),
      modelType: 'auto',
      includeSegmentation: false,
      includePredictions: false,
      predictionHorizon: 'lifetime',
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      status: "healthy",
      service: "CLV Calculation Service",
      version: "2.0.0",
      performance: {
        executionTimeMs: Math.round(executionTime * 100) / 100,
        targetMs: 500,
        status: executionTime < 500 ? "optimal" : "degraded",
      },
      capabilities: {
        modelTypes: ['traditional', 'ml_ensemble', 'deep_learning', 'auto'],
        predictionHorizons: ['12m', '24m', 'lifetime'],
        segmentationSupport: true,
        realTimePredictions: true,
        multiTenantSupport: true,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("CLV health check error:", error);
    
    ctx.response.status = 503;
    ctx.response.body = {
      success: false,
      status: "unhealthy",
      service: "CLV Calculation Service",
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
});

export default router;
