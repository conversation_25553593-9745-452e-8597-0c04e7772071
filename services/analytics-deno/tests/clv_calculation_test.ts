// Enhanced CLV Calculation Service Tests - Phase 2 Implementation
// Week 11-12: Comprehensive testing for CLV prediction models and API endpoints

import { assertEquals, assertExists, assert } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { CLVCalculationService } from "../src/services/clvCalculationService.ts";
import { initializeDatabase, closeDatabase } from "../src/utils/database.ts";

// Test configuration
const TEST_TENANT_ID = "00000000-0000-0000-0000-000000000001";
const TEST_DATE_FROM = "2024-01-01T00:00:00Z";
const TEST_DATE_TO = "2024-12-31T23:59:59Z";

// Initialize test environment
Deno.test({
  name: "CLV Calculation Service - Setup",
  async fn() {
    await initializeDatabase();
    console.log("Database initialized for CLV tests");
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test CLV service instantiation
Deno.test({
  name: "CLV Service - Instantiation",
  fn() {
    const clvService = new CLVCalculationService();
    assertExists(clvService);
    console.log("✓ CLV service instantiated successfully");
  },
});

// Test CLV calculation with default parameters
Deno.test({
  name: "CLV Service - Basic Calculation",
  async fn() {
    const clvService = new CLVCalculationService();
    
    const startTime = performance.now();
    const result = await clvService.calculateCLV({
      tenantId: TEST_TENANT_ID,
      dateFrom: TEST_DATE_FROM,
      dateTo: TEST_DATE_TO,
      modelType: 'auto',
      includeSegmentation: true,
      includePredictions: true,
      predictionHorizon: 'lifetime',
    });
    const executionTime = performance.now() - startTime;
    
    // Validate result structure
    assertExists(result);
    assertExists(result.predictions);
    assertExists(result.segments);
    assertExists(result.overview);
    assertExists(result.insights);
    assertExists(result.modelPerformance);
    assertExists(result.generatedAt);
    
    // Validate performance requirement (<500ms)
    assert(executionTime < 500, `Execution time ${executionTime.toFixed(2)}ms exceeds 500ms target`);
    
    console.log(`✓ CLV calculation completed in ${executionTime.toFixed(2)}ms`);
    console.log(`✓ Total customers: ${result.overview.totalCustomers}`);
    console.log(`✓ Total predicted value: $${result.overview.totalPredictedValue.toFixed(2)}`);
    console.log(`✓ Model accuracy: ${(result.overview.modelAccuracy * 100).toFixed(1)}%`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test CLV predictions with different model types
Deno.test({
  name: "CLV Service - Model Type Variations",
  async fn() {
    const clvService = new CLVCalculationService();
    const modelTypes = ['traditional', 'ml_ensemble', 'deep_learning', 'auto'];
    
    for (const modelType of modelTypes) {
      const startTime = performance.now();
      const result = await clvService.calculateCLV({
        tenantId: TEST_TENANT_ID,
        dateFrom: TEST_DATE_FROM,
        dateTo: TEST_DATE_TO,
        modelType: modelType as 'traditional' | 'ml_ensemble' | 'deep_learning' | 'auto',
        includeSegmentation: false,
        includePredictions: true,
        predictionHorizon: 'lifetime',
      });
      const executionTime = performance.now() - startTime;
      
      assertExists(result);
      assertEquals(result.modelPerformance.modelType, modelType);
      assert(executionTime < 500, `${modelType} model execution time ${executionTime.toFixed(2)}ms exceeds 500ms`);
      
      console.log(`✓ ${modelType} model: ${executionTime.toFixed(2)}ms, accuracy: ${(result.modelPerformance.accuracy * 100).toFixed(1)}%`);
    }
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test CLV segmentation functionality
Deno.test({
  name: "CLV Service - Segmentation Analysis",
  async fn() {
    const clvService = new CLVCalculationService();
    
    const result = await clvService.calculateCLV({
      tenantId: TEST_TENANT_ID,
      dateFrom: TEST_DATE_FROM,
      dateTo: TEST_DATE_TO,
      modelType: 'auto',
      includeSegmentation: true,
      includePredictions: false,
      predictionHorizon: 'lifetime',
    });
    
    assertExists(result.segments);
    assert(Array.isArray(result.segments));
    
    // Validate segment structure
    if (result.segments.length > 0) {
      const segment = result.segments[0];
      assertExists(segment.segmentName);
      assertExists(segment.segmentType);
      assertExists(segment.segmentTier);
      assert(segment.customerCount >= 0);
      assert(segment.avgClv >= 0);
      assert(segment.avgChurnProbability >= 0 && segment.avgChurnProbability <= 1);
      assert(segment.avgRetentionProbability >= 0 && segment.avgRetentionProbability <= 1);
    }
    
    console.log(`✓ Segmentation analysis: ${result.segments.length} segments identified`);
    result.segments.forEach(segment => {
      console.log(`  - ${segment.segmentName} (${segment.segmentTier}): ${segment.customerCount} customers, avg CLV: $${segment.avgClv.toFixed(2)}`);
    });
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test CLV insights generation
Deno.test({
  name: "CLV Service - Business Insights",
  async fn() {
    const clvService = new CLVCalculationService();
    
    const result = await clvService.calculateCLV({
      tenantId: TEST_TENANT_ID,
      dateFrom: TEST_DATE_FROM,
      dateTo: TEST_DATE_TO,
      modelType: 'auto',
      includeSegmentation: true,
      includePredictions: true,
      predictionHorizon: 'lifetime',
    });
    
    assertExists(result.insights);
    assertExists(result.insights.topValueSegment);
    assertExists(result.insights.highestRiskSegment);
    assertExists(result.insights.growthOpportunities);
    assertExists(result.insights.retentionRecommendations);
    assertExists(result.insights.valueOptimizationActions);
    
    assert(Array.isArray(result.insights.growthOpportunities));
    assert(Array.isArray(result.insights.retentionRecommendations));
    assert(Array.isArray(result.insights.valueOptimizationActions));
    
    console.log(`✓ Business insights generated:`);
    console.log(`  - Top value segment: ${result.insights.topValueSegment}`);
    console.log(`  - Highest risk segment: ${result.insights.highestRiskSegment}`);
    console.log(`  - Growth opportunities: ${result.insights.growthOpportunities.length}`);
    console.log(`  - Retention recommendations: ${result.insights.retentionRecommendations.length}`);
    console.log(`  - Value optimization actions: ${result.insights.valueOptimizationActions.length}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test CLV customer filtering
Deno.test({
  name: "CLV Service - Customer Filtering",
  async fn() {
    const clvService = new CLVCalculationService();
    
    // Test with minimum CLV filter
    const resultWithMinClv = await clvService.calculateCLV({
      tenantId: TEST_TENANT_ID,
      dateFrom: TEST_DATE_FROM,
      dateTo: TEST_DATE_TO,
      modelType: 'auto',
      includeSegmentation: false,
      includePredictions: true,
      predictionHorizon: 'lifetime',
      customerFilter: {
        minClv: 100,
      },
    });
    
    assertExists(resultWithMinClv.predictions);
    
    // Validate that all predictions meet minimum CLV requirement
    resultWithMinClv.predictions.forEach(prediction => {
      assert(prediction.predictedClvLifetime >= 100, `Prediction ${prediction.customerId} CLV ${prediction.predictedClvLifetime} below minimum 100`);
    });
    
    // Test with churn risk threshold
    const resultWithChurnFilter = await clvService.calculateCLV({
      tenantId: TEST_TENANT_ID,
      dateFrom: TEST_DATE_FROM,
      dateTo: TEST_DATE_TO,
      modelType: 'auto',
      includeSegmentation: false,
      includePredictions: true,
      predictionHorizon: 'lifetime',
      customerFilter: {
        churnRiskThreshold: 0.7,
      },
    });
    
    assertExists(resultWithChurnFilter.predictions);
    
    // Validate that all predictions meet churn risk threshold
    resultWithChurnFilter.predictions.forEach(prediction => {
      assert(prediction.churnProbability >= 0.7, `Prediction ${prediction.customerId} churn probability ${prediction.churnProbability} below threshold 0.7`);
    });
    
    console.log(`✓ Customer filtering: ${resultWithMinClv.predictions.length} customers with CLV >= $100`);
    console.log(`✓ Customer filtering: ${resultWithChurnFilter.predictions.length} customers with churn risk >= 70%`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test prediction horizon variations
Deno.test({
  name: "CLV Service - Prediction Horizons",
  async fn() {
    const clvService = new CLVCalculationService();
    const horizons = ['12m', '24m', 'lifetime'] as const;
    
    for (const horizon of horizons) {
      const result = await clvService.calculateCLV({
        tenantId: TEST_TENANT_ID,
        dateFrom: TEST_DATE_FROM,
        dateTo: TEST_DATE_TO,
        modelType: 'auto',
        includeSegmentation: false,
        includePredictions: true,
        predictionHorizon: horizon,
      });
      
      assertExists(result.predictions);
      
      // Validate prediction structure based on horizon
      if (result.predictions.length > 0) {
        const prediction = result.predictions[0];
        assertExists(prediction.predictedClv12m);
        assertExists(prediction.predictedClv24m);
        assertExists(prediction.predictedClvLifetime);
        
        // Validate logical relationship between horizons
        assert(prediction.predictedClv12m <= prediction.predictedClv24m, "12m CLV should be <= 24m CLV");
        assert(prediction.predictedClv24m <= prediction.predictedClvLifetime, "24m CLV should be <= lifetime CLV");
      }
      
      console.log(`✓ ${horizon} prediction horizon: ${result.predictions.length} predictions generated`);
    }
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test multi-tenant isolation
Deno.test({
  name: "CLV Service - Multi-tenant Isolation",
  async fn() {
    const clvService = new CLVCalculationService();
    const tenant1 = "00000000-0000-0000-0000-000000000001";
    const tenant2 = "00000000-0000-0000-0000-000000000002";
    
    const result1 = await clvService.calculateCLV({
      tenantId: tenant1,
      dateFrom: TEST_DATE_FROM,
      dateTo: TEST_DATE_TO,
      modelType: 'auto',
      includeSegmentation: true,
      includePredictions: true,
      predictionHorizon: 'lifetime',
    });
    
    const result2 = await clvService.calculateCLV({
      tenantId: tenant2,
      dateFrom: TEST_DATE_FROM,
      dateTo: TEST_DATE_TO,
      modelType: 'auto',
      includeSegmentation: true,
      includePredictions: true,
      predictionHorizon: 'lifetime',
    });
    
    // Validate that results are tenant-specific
    assertExists(result1);
    assertExists(result2);
    
    // Results should be different for different tenants (unless both have no data)
    const tenant1HasData = result1.overview.totalCustomers > 0;
    const tenant2HasData = result2.overview.totalCustomers > 0;
    
    if (tenant1HasData && tenant2HasData) {
      // If both tenants have data, they should have different customer IDs
      const tenant1CustomerIds = new Set(result1.predictions.map(p => p.customerId));
      const tenant2CustomerIds = new Set(result2.predictions.map(p => p.customerId));
      
      // No overlap in customer IDs between tenants
      const overlap = [...tenant1CustomerIds].filter(id => tenant2CustomerIds.has(id));
      assertEquals(overlap.length, 0, "Customer IDs should not overlap between tenants");
    }
    
    console.log(`✓ Multi-tenant isolation: Tenant 1 (${result1.overview.totalCustomers} customers), Tenant 2 (${result2.overview.totalCustomers} customers)`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Cleanup test environment
Deno.test({
  name: "CLV Calculation Service - Cleanup",
  async fn() {
    await closeDatabase();
    console.log("Database connections closed");
  },
  sanitizeResources: false,
  sanitizeOps: false,
});
