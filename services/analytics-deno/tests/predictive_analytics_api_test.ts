// Enhanced Predictive Analytics API Tests - Phase 2 Week 15-16
// Comprehensive API endpoint testing for ML predictions, churn analysis, revenue forecasting, and anomaly detection
// Tests RESTful endpoints following established patterns from cohort analysis, CLV calculation, and funnel analysis

import { assertEquals, assertExists, assert } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { Application } from "@oak/oak";
import { setupRoutes } from "../src/routes/index.ts";
import { initializeDatabase, closeDatabase } from "../src/utils/database.ts";

// Test configuration
const TEST_TENANT_ID = "00000000-0000-0000-0000-000000000001";
const TEST_CUSTOMER_ID = "customer_001";
const BASE_URL = "http://localhost:3002";

// Helper function to make HTTP requests
async function makeRequest(path: string, options: RequestInit = {}): Promise<Response> {
  const url = `${BASE_URL}${path}`;
  return await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });
}

// Initialize test environment
Deno.test({
  name: "Predictive Analytics API - Setup",
  async fn() {
    await initializeDatabase();
    console.log("Database initialized for predictive analytics API tests");
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// GENERAL PREDICTION API TESTS
// =====================================================

// Test prediction generation endpoint
Deno.test({
  name: "API - POST /api/enhanced-analytics/predictions/generate",
  async fn() {
    const requestBody = {
      tenantId: TEST_TENANT_ID,
      modelType: 'churn_prediction',
      entityId: TEST_CUSTOMER_ID,
      entityType: 'customer',
      predictionHorizonDays: 30,
      includeConfidence: true,
      includeExplanation: true
    };

    const startTime = performance.now();
    const response = await makeRequest('/api/enhanced-analytics/predictions/generate', {
      method: 'POST',
      body: JSON.stringify(requestBody)
    });
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);
    assertExists(data.metadata);
    assertExists(data.timestamp);

    // Validate prediction data
    const prediction = data.data;
    assertExists(prediction.predictionId);
    assertExists(prediction.predictedValue);
    assertExists(prediction.confidenceScore);
    assertEquals(prediction.predictionType, 'churn_prediction');

    // Validate metadata
    assertExists(data.metadata.executionTime);
    assertEquals(data.metadata.modelType, 'churn_prediction');
    assertExists(data.metadata.confidenceScore);

    assert(executionTime < 1000, `API response should be fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Prediction generation API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Response time: ${data.metadata.executionTime}`);
    console.log(`  - Prediction ID: ${prediction.predictionId}`);
    console.log(`  - Confidence: ${prediction.confidenceScore}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test batch prediction endpoint
Deno.test({
  name: "API - POST /api/enhanced-analytics/predictions/batch",
  async fn() {
    const requestBody = {
      tenantId: TEST_TENANT_ID,
      modelType: 'behavior_prediction',
      entityIds: [TEST_CUSTOMER_ID, 'customer_002', 'customer_003'],
      predictionHorizonDays: 7,
      includeConfidence: true
    };

    const startTime = performance.now();
    const response = await makeRequest('/api/enhanced-analytics/predictions/batch', {
      method: 'POST',
      body: JSON.stringify(requestBody)
    });
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);
    assertEquals(data.data.length, 3);

    // Validate metadata
    assertEquals(data.metadata.entityCount, 3);
    assertEquals(data.metadata.modelType, 'behavior_prediction');
    assertExists(data.metadata.avgConfidence);

    assert(executionTime < 2000, `Batch API response should be efficient (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Batch prediction API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Entities processed: ${data.metadata.entityCount}`);
    console.log(`  - Average confidence: ${data.metadata.avgConfidence}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test invalid request validation
Deno.test({
  name: "API - Prediction Generation Validation",
  async fn() {
    const invalidRequestBody = {
      tenantId: "invalid-uuid",
      modelType: 'invalid_model_type',
      predictionHorizonDays: -1
    };

    const response = await makeRequest('/api/enhanced-analytics/predictions/generate', {
      method: 'POST',
      body: JSON.stringify(invalidRequestBody)
    });

    // Should return validation error
    assertEquals(response.status, 400);
    
    const data = await response.json();
    assertEquals(data.success, false);
    assertExists(data.error);
    assertExists(data.details); // Zod validation errors

    console.log(`✓ Prediction validation test passed - properly rejected invalid request`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// CHURN PREDICTION API TESTS
// =====================================================

// Test churn prediction endpoint
Deno.test({
  name: "API - GET /api/enhanced-analytics/predictions/churn",
  async fn() {
    const queryParams = new URLSearchParams({
      tenantId: TEST_TENANT_ID,
      customerId: TEST_CUSTOMER_ID,
      includeRiskFactors: 'true',
      includeRecommendations: 'true'
    });

    const startTime = performance.now();
    const response = await makeRequest(`/api/enhanced-analytics/predictions/churn?${queryParams}`);
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);
    assert(data.data.length > 0);

    // Validate churn prediction data
    const prediction = data.data[0];
    assertExists(prediction.customerId);
    assertExists(prediction.churnProbability);
    assertExists(prediction.riskLevel);
    assertExists(prediction.primaryRiskFactors);
    assertExists(prediction.recommendedActions);

    // Validate metadata
    assertExists(data.metadata.customersAnalyzed);
    assertExists(data.metadata.avgChurnProbability);

    assert(executionTime < 1000, `Churn prediction API should be fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Churn prediction API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Customers analyzed: ${data.metadata.customersAnalyzed}`);
    console.log(`  - High-risk customers: ${data.metadata.highRiskCustomers}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test churn summary endpoint
Deno.test({
  name: "API - GET /api/enhanced-analytics/predictions/churn/summary",
  async fn() {
    const queryParams = new URLSearchParams({
      tenantId: TEST_TENANT_ID
    });

    const startTime = performance.now();
    const response = await makeRequest(`/api/enhanced-analytics/predictions/churn/summary?${queryParams}`);
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);

    // Validate summary data
    assertExists(data.data.totalCustomers);
    assertExists(data.data.riskDistribution);
    assertExists(data.data.avgChurnProbability);
    assertExists(data.data.highRiskCustomers);

    // Validate risk distribution
    const riskDist = data.data.riskDistribution;
    assertExists(riskDist.critical);
    assertExists(riskDist.high);
    assertExists(riskDist.medium);
    assertExists(riskDist.low);

    assert(executionTime < 1000, `Churn summary API should be fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Churn summary API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Total customers: ${data.data.totalCustomers}`);
    console.log(`  - High-risk customers: ${data.data.highRiskCustomers}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// REVENUE FORECASTING API TESTS
// =====================================================

// Test revenue forecast endpoint
Deno.test({
  name: "API - GET /api/enhanced-analytics/predictions/revenue-forecast",
  async fn() {
    const queryParams = new URLSearchParams({
      tenantId: TEST_TENANT_ID,
      forecastType: 'daily',
      forecastHorizonDays: '30',
      includeConfidenceInterval: 'true',
      includeComponents: 'true',
      algorithm: 'ensemble'
    });

    const startTime = performance.now();
    const response = await makeRequest(`/api/enhanced-analytics/predictions/revenue-forecast?${queryParams}`);
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);
    assertEquals(data.data.length, 30);

    // Validate forecast data
    const forecast = data.data[0];
    assertExists(forecast.forecastName);
    assertExists(forecast.forecastType);
    assertExists(forecast.forecastedRevenue);
    assertExists(forecast.lowerBound);
    assertExists(forecast.upperBound);

    // Validate metadata
    assertEquals(data.metadata.forecastType, 'daily');
    assertEquals(data.metadata.forecastPoints, 30);
    assertExists(data.metadata.totalForecastedRevenue);

    assert(executionTime < 1500, `Revenue forecast API should be efficient (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Revenue forecast API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Forecast points: ${data.metadata.forecastPoints}`);
    console.log(`  - Total forecasted revenue: $${data.metadata.totalForecastedRevenue}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test forecast accuracy endpoint
Deno.test({
  name: "API - GET /api/enhanced-analytics/predictions/revenue-forecast/accuracy",
  async fn() {
    const queryParams = new URLSearchParams({
      tenantId: TEST_TENANT_ID,
      forecastType: 'daily'
    });

    const startTime = performance.now();
    const response = await makeRequest(`/api/enhanced-analytics/predictions/revenue-forecast/accuracy?${queryParams}`);
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);

    // Validate accuracy data
    assertExists(data.data.avgAccuracy);
    assertExists(data.data.minAccuracy);
    assertExists(data.data.maxAccuracy);
    assertExists(data.data.forecastCount);

    assert(executionTime < 1000, `Forecast accuracy API should be fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Forecast accuracy API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Average accuracy: ${(data.data.avgAccuracy * 100).toFixed(2)}%`);
    console.log(`  - Forecast count: ${data.data.forecastCount}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// BEHAVIOR PREDICTION API TESTS
// =====================================================

// Test behavior prediction endpoint
Deno.test({
  name: "API - GET /api/enhanced-analytics/predictions/behavior",
  async fn() {
    const queryParams = new URLSearchParams({
      tenantId: TEST_TENANT_ID,
      customerId: TEST_CUSTOMER_ID,
      includeRecommendations: 'true',
      includeTimingPredictions: 'true'
    });

    const startTime = performance.now();
    const response = await makeRequest(`/api/enhanced-analytics/predictions/behavior?${queryParams}`);
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);
    assert(data.data.length > 0);

    // Validate behavior prediction data
    const prediction = data.data[0];
    assertExists(prediction.customerId);
    assertExists(prediction.behaviorType);
    assertExists(prediction.predictedBehavior);
    assertExists(prediction.confidenceScore);
    assertExists(prediction.recommendedAction);

    // Validate metadata
    assertEquals(data.metadata.customerId, TEST_CUSTOMER_ID);
    assertExists(data.metadata.avgConfidence);

    assert(executionTime < 1000, `Behavior prediction API should be fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Behavior prediction API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Predictions generated: ${data.metadata.predictionsGenerated}`);
    console.log(`  - Average confidence: ${data.metadata.avgConfidence}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// ANOMALY DETECTION API TESTS
// =====================================================

// Test anomaly detection endpoint
Deno.test({
  name: "API - GET /api/enhanced-analytics/predictions/anomalies",
  async fn() {
    const queryParams = new URLSearchParams({
      tenantId: TEST_TENANT_ID,
      severityThreshold: '0.7',
      timeWindowHours: '24',
      includeContext: 'true'
    });

    const startTime = performance.now();
    const response = await makeRequest(`/api/enhanced-analytics/predictions/anomalies?${queryParams}`);
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);

    // Validate metadata (anomalies may be empty, which is normal)
    assertExists(data.metadata.anomaliesDetected);
    assertExists(data.metadata.timeWindowHours);

    assert(executionTime < 1500, `Anomaly detection API should be efficient (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Anomaly detection API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Anomalies detected: ${data.metadata.anomaliesDetected}`);
    console.log(`  - Critical anomalies: ${data.metadata.criticalAnomalies}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test anomaly summary endpoint
Deno.test({
  name: "API - GET /api/enhanced-analytics/predictions/anomalies/summary",
  async fn() {
    const queryParams = new URLSearchParams({
      tenantId: TEST_TENANT_ID,
      timeWindowHours: '24'
    });

    const startTime = performance.now();
    const response = await makeRequest(`/api/enhanced-analytics/predictions/anomalies/summary?${queryParams}`);
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);

    // Validate summary data
    assertExists(data.data.totalAnomalies);
    assertExists(data.data.severityDistribution);
    assertExists(data.data.typeDistribution);

    assert(executionTime < 1000, `Anomaly summary API should be fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Anomaly summary API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Total anomalies: ${data.data.totalAnomalies}`);
    console.log(`  - Requires attention: ${data.data.requiresAttention}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// MODEL MANAGEMENT API TESTS
// =====================================================

// Test models endpoint
Deno.test({
  name: "API - GET /api/enhanced-analytics/predictions/models",
  async fn() {
    const queryParams = new URLSearchParams({
      tenantId: TEST_TENANT_ID
    });

    const startTime = performance.now();
    const response = await makeRequest(`/api/enhanced-analytics/predictions/models?${queryParams}`);
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);

    // Validate metadata
    assertExists(data.metadata.modelCount);
    assertExists(data.metadata.activeModels);
    assertExists(data.metadata.productionModels);

    assert(executionTime < 500, `Models API should be fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Models API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Total models: ${data.metadata.modelCount}`);
    console.log(`  - Active models: ${data.metadata.activeModels}`);
    console.log(`  - Production models: ${data.metadata.productionModels}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test health check endpoint
Deno.test({
  name: "API - GET /api/enhanced-analytics/predictions/health",
  async fn() {
    const startTime = performance.now();
    const response = await makeRequest('/api/enhanced-analytics/predictions/health');
    const executionTime = performance.now() - startTime;

    // Validate response
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertExists(data);
    assertEquals(data.success, true);
    assertExists(data.data);

    // Validate health data
    assertEquals(data.data.status, 'healthy');
    assertEquals(data.data.service, 'predictive-analytics');
    assertExists(data.data.version);
    assertExists(data.data.uptime);

    assert(executionTime < 100, `Health check should be very fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Health check API test passed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Service status: ${data.data.status}`);
    console.log(`  - Service version: ${data.data.version}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Cleanup test environment
Deno.test({
  name: "Predictive Analytics API - Cleanup",
  async fn() {
    await closeDatabase();
    console.log("✓ API test environment cleaned up successfully");
  },
  sanitizeResources: false,
  sanitizeOps: false,
});
