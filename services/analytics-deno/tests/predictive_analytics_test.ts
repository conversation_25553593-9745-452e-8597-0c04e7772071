// Enhanced Predictive Analytics Service Tests - Phase 2 Week 15-16
// Comprehensive testing for ML predictions, churn analysis, revenue forecasting, and anomaly detection
// Follows established patterns from cohort analysis, CLV calculation, and funnel analysis tests

import { assertEquals, assertExists, assert } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { PredictiveAnalyticsService } from "../src/services/predictiveAnalyticsService.ts";
import { initializeDatabase, closeDatabase } from "../src/utils/database.ts";
import { initializeRedis, closeRedis } from "../src/utils/redis.ts";

// Test configuration
const TEST_TENANT_ID = "00000000-0000-0000-0000-000000000001";
const TEST_CUSTOMER_ID = "customer_001";
const TEST_MODEL_ID = "model_001";
const TEST_DATE_FROM = "2024-01-01T00:00:00Z";
const TEST_DATE_TO = "2024-12-31T23:59:59Z";

// Initialize test environment
Deno.test({
  name: "Predictive Analytics Service - Setup",
  async fn() {
    await initializeDatabase();
    await initializeRedis();
    console.log("Database and Redis initialized for predictive analytics tests");
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test service instantiation
Deno.test({
  name: "Predictive Analytics Service - Instantiation",
  fn() {
    const predictiveService = new PredictiveAnalyticsService();
    assertExists(predictiveService);
    console.log("✓ Predictive analytics service instantiated successfully");
  },
});

// =====================================================
// GENERAL PREDICTION TESTS
// =====================================================

// Test general prediction generation
Deno.test({
  name: "Predictive Analytics - General Prediction Generation",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();
    
    const startTime = performance.now();
    const result = await predictiveService.generatePrediction({
      tenantId: TEST_TENANT_ID,
      modelType: 'churn_prediction',
      entityId: TEST_CUSTOMER_ID,
      entityType: 'customer',
      predictionHorizonDays: 30,
      includeConfidence: true,
      includeExplanation: true
    });
    const executionTime = performance.now() - startTime;

    // Validate prediction result structure
    assertExists(result);
    assertExists(result.predictionId);
    assertExists(result.predictedValue);
    assertExists(result.confidenceScore);
    assertEquals(result.predictionType, 'churn_prediction');
    assertExists(result.predictedAt);

    // Validate prediction values
    assert(result.predictedValue >= 0 && result.predictedValue <= 1, "Churn prediction should be between 0 and 1");
    assert(result.confidenceScore >= 0 && result.confidenceScore <= 1, "Confidence score should be between 0 and 1");
    assert(executionTime < 500, `Prediction generation should be fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ General prediction generated successfully in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Prediction ID: ${result.predictionId}`);
    console.log(`  - Predicted Value: ${result.predictedValue.toFixed(4)}`);
    console.log(`  - Confidence Score: ${result.confidenceScore.toFixed(4)}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test batch prediction generation
Deno.test({
  name: "Predictive Analytics - Batch Prediction Generation",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();
    
    const entityIds = [TEST_CUSTOMER_ID, "customer_002", "customer_003"];
    
    const startTime = performance.now();
    const results = await predictiveService.generateBatchPredictions(
      TEST_TENANT_ID,
      'behavior_prediction',
      entityIds,
      {
        predictionHorizonDays: 7,
        includeConfidence: true
      }
    );
    const executionTime = performance.now() - startTime;

    // Validate batch results
    assertExists(results);
    assertEquals(results.length, entityIds.length);
    
    for (const result of results) {
      assertExists(result.predictionId);
      assertExists(result.predictedValue);
      assertExists(result.confidenceScore);
      assertEquals(result.predictionType, 'behavior_prediction');
      assert(result.confidenceScore >= 0 && result.confidenceScore <= 1);
    }

    const avgConfidence = results.reduce((sum, r) => sum + r.confidenceScore, 0) / results.length;
    assert(executionTime < 2000, `Batch prediction should be efficient (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Batch predictions generated successfully in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Entities processed: ${results.length}`);
    console.log(`  - Average confidence: ${avgConfidence.toFixed(4)}`);
    console.log(`  - Avg time per prediction: ${(executionTime / results.length).toFixed(2)}ms`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// CHURN PREDICTION TESTS
// =====================================================

// Test customer churn prediction
Deno.test({
  name: "Predictive Analytics - Customer Churn Prediction",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();
    
    const startTime = performance.now();
    const results = await predictiveService.predictCustomerChurn({
      tenantId: TEST_TENANT_ID,
      customerId: TEST_CUSTOMER_ID,
      includeRiskFactors: true,
      includeRecommendations: true,
      riskThreshold: 0.5
    });
    const executionTime = performance.now() - startTime;

    // Validate churn prediction results
    assertExists(results);
    assert(results.length > 0, "Should return at least one churn prediction");
    
    const prediction = results[0];
    assertExists(prediction.customerId);
    assertExists(prediction.churnProbability);
    assertExists(prediction.riskLevel);
    assertExists(prediction.primaryRiskFactors);
    assertExists(prediction.recommendedActions);
    assertExists(prediction.interventionPriority);

    // Validate churn prediction values
    assert(prediction.churnProbability >= 0 && prediction.churnProbability <= 1, "Churn probability should be between 0 and 1");
    assert(['low', 'medium', 'high', 'critical'].includes(prediction.riskLevel), "Risk level should be valid");
    assert(prediction.engagementScore >= 0 && prediction.engagementScore <= 1, "Engagement score should be between 0 and 1");
    assert(prediction.interventionPriority >= 1 && prediction.interventionPriority <= 10, "Intervention priority should be 1-10");
    assert(executionTime < 500, `Churn prediction should be fast (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Customer churn prediction completed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Customer ID: ${prediction.customerId}`);
    console.log(`  - Churn Probability: ${(prediction.churnProbability * 100).toFixed(2)}%`);
    console.log(`  - Risk Level: ${prediction.riskLevel}`);
    console.log(`  - Engagement Score: ${prediction.engagementScore.toFixed(4)}`);
    console.log(`  - Intervention Priority: ${prediction.interventionPriority}/10`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test bulk churn prediction
Deno.test({
  name: "Predictive Analytics - Bulk Churn Prediction",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();
    
    const startTime = performance.now();
    const results = await predictiveService.predictCustomerChurn({
      tenantId: TEST_TENANT_ID,
      includeRiskFactors: true,
      includeRecommendations: true
    });
    const executionTime = performance.now() - startTime;

    // Validate bulk churn results
    assertExists(results);
    assert(results.length > 0, "Should return multiple churn predictions");

    // Analyze risk distribution
    const riskDistribution = {
      critical: results.filter(p => p.riskLevel === 'critical').length,
      high: results.filter(p => p.riskLevel === 'high').length,
      medium: results.filter(p => p.riskLevel === 'medium').length,
      low: results.filter(p => p.riskLevel === 'low').length
    };

    const avgChurnProbability = results.reduce((sum, p) => sum + p.churnProbability, 0) / results.length;
    const highRiskCustomers = riskDistribution.critical + riskDistribution.high;

    assert(executionTime < 2000, `Bulk churn prediction should be efficient (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Bulk churn prediction completed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Customers analyzed: ${results.length}`);
    console.log(`  - Average churn probability: ${(avgChurnProbability * 100).toFixed(2)}%`);
    console.log(`  - High-risk customers: ${highRiskCustomers} (${((highRiskCustomers / results.length) * 100).toFixed(1)}%)`);
    console.log(`  - Risk distribution: Critical(${riskDistribution.critical}), High(${riskDistribution.high}), Medium(${riskDistribution.medium}), Low(${riskDistribution.low})`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// REVENUE FORECASTING TESTS
// =====================================================

// Test revenue forecasting
Deno.test({
  name: "Predictive Analytics - Revenue Forecasting",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();
    
    const startTime = performance.now();
    const results = await predictiveService.generateRevenueForecast({
      tenantId: TEST_TENANT_ID,
      forecastType: 'daily',
      forecastHorizonDays: 30,
      includeConfidenceInterval: true,
      includeComponents: true,
      algorithm: 'ensemble'
    });
    const executionTime = performance.now() - startTime;

    // Validate revenue forecast results
    assertExists(results);
    assertEquals(results.length, 30, "Should return 30 daily forecasts");
    
    const forecast = results[0];
    assertExists(forecast.forecastName);
    assertExists(forecast.forecastType);
    assertExists(forecast.forecastedRevenue);
    assertExists(forecast.lowerBound);
    assertExists(forecast.upperBound);
    assertExists(forecast.confidenceLevel);
    assertExists(forecast.forecastDate);

    // Validate forecast values
    assert(forecast.forecastedRevenue > 0, "Forecasted revenue should be positive");
    assert(forecast.lowerBound! < forecast.forecastedRevenue, "Lower bound should be less than forecast");
    assert(forecast.upperBound! > forecast.forecastedRevenue, "Upper bound should be greater than forecast");
    assert(forecast.confidenceLevel >= 0.8 && forecast.confidenceLevel <= 1.0, "Confidence level should be high");
    assert(executionTime < 1000, `Revenue forecasting should be efficient (${executionTime.toFixed(2)}ms)`);

    const totalForecastedRevenue = results.reduce((sum, f) => sum + f.forecastedRevenue, 0);
    const avgAccuracy = results.reduce((sum, f) => sum + (f.forecastAccuracy || 0), 0) / results.length;

    console.log(`✓ Revenue forecasting completed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Forecast horizon: ${results.length} days`);
    console.log(`  - Total forecasted revenue: $${totalForecastedRevenue.toFixed(2)}`);
    console.log(`  - Average daily forecast: $${(totalForecastedRevenue / results.length).toFixed(2)}`);
    console.log(`  - Average accuracy: ${(avgAccuracy * 100).toFixed(2)}%`);
    console.log(`  - Confidence level: ${(forecast.confidenceLevel * 100).toFixed(1)}%`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test different forecast types
Deno.test({
  name: "Predictive Analytics - Multiple Forecast Types",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();
    const forecastTypes: Array<'daily' | 'weekly' | 'monthly' | 'quarterly'> = ['daily', 'weekly', 'monthly'];
    
    for (const forecastType of forecastTypes) {
      const startTime = performance.now();
      const results = await predictiveService.generateRevenueForecast({
        tenantId: TEST_TENANT_ID,
        forecastType,
        forecastHorizonDays: forecastType === 'daily' ? 7 : forecastType === 'weekly' ? 4 : 3,
        includeConfidenceInterval: true
      });
      const executionTime = performance.now() - startTime;

      assertExists(results);
      assert(results.length > 0, `Should return ${forecastType} forecasts`);
      assertEquals(results[0].forecastType, forecastType);
      assert(executionTime < 500, `${forecastType} forecasting should be fast`);

      const avgRevenue = results.reduce((sum, f) => sum + f.forecastedRevenue, 0) / results.length;
      console.log(`✓ ${forecastType} forecast: ${results.length} periods, avg $${avgRevenue.toFixed(2)} (${executionTime.toFixed(2)}ms)`);
    }
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// BEHAVIOR PREDICTION TESTS
// =====================================================

// Test customer behavior prediction
Deno.test({
  name: "Predictive Analytics - Customer Behavior Prediction",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();

    const startTime = performance.now();
    const results = await predictiveService.predictCustomerBehavior({
      tenantId: TEST_TENANT_ID,
      customerId: TEST_CUSTOMER_ID,
      behaviorTypes: ['next_purchase', 'product_interest', 'channel_preference'],
      includeRecommendations: true,
      includeTimingPredictions: true
    });
    const executionTime = performance.now() - startTime;

    // Validate behavior prediction results
    assertExists(results);
    assert(results.length > 0, "Should return behavior predictions");

    const prediction = results[0];
    assertExists(prediction.customerId);
    assertExists(prediction.behaviorType);
    assertExists(prediction.predictedBehavior);
    assertExists(prediction.confidenceScore);
    assertExists(prediction.recommendedAction);
    assertExists(prediction.actionPriority);
    assertExists(prediction.recommendedProducts);
    assertExists(prediction.recommendedChannels);

    // Validate behavior prediction values
    assertEquals(prediction.customerId, TEST_CUSTOMER_ID);
    assert(prediction.confidenceScore >= 0 && prediction.confidenceScore <= 1, "Confidence score should be between 0 and 1");
    assert(prediction.actionPriority >= 1 && prediction.actionPriority <= 10, "Action priority should be 1-10");
    assert(Array.isArray(prediction.recommendedProducts), "Recommended products should be an array");
    assert(Array.isArray(prediction.recommendedChannels), "Recommended channels should be an array");
    assert(executionTime < 500, `Behavior prediction should be fast (${executionTime.toFixed(2)}ms)`);

    const avgConfidence = results.reduce((sum, p) => sum + p.confidenceScore, 0) / results.length;
    const highPriorityActions = results.filter(p => p.actionPriority >= 8).length;

    console.log(`✓ Customer behavior prediction completed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Customer ID: ${prediction.customerId}`);
    console.log(`  - Predictions generated: ${results.length}`);
    console.log(`  - Average confidence: ${avgConfidence.toFixed(4)}`);
    console.log(`  - High priority actions: ${highPriorityActions}`);
    console.log(`  - Behavior types: ${results.map(p => p.behaviorType).join(', ')}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test behavior prediction timing
Deno.test({
  name: "Predictive Analytics - Behavior Prediction Timing",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();

    const startTime = performance.now();
    const results = await predictiveService.predictCustomerBehavior({
      tenantId: TEST_TENANT_ID,
      customerId: TEST_CUSTOMER_ID,
      behaviorTypes: ['next_purchase'],
      includeTimingPredictions: true
    });
    const executionTime = performance.now() - startTime;

    // Validate timing predictions
    assertExists(results);
    assert(results.length > 0, "Should return timing predictions");

    const prediction = results[0];
    if (prediction.predictedTiming) {
      const predictedTime = new Date(prediction.predictedTiming);
      const now = new Date();
      assert(predictedTime > now, "Predicted timing should be in the future");
    }

    if (prediction.optimalContactTime) {
      const contactTime = new Date(prediction.optimalContactTime);
      const now = new Date();
      assert(contactTime > now, "Optimal contact time should be in the future");
    }

    console.log(`✓ Behavior prediction timing validated in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Predicted timing: ${prediction.predictedTiming || 'N/A'}`);
    console.log(`  - Optimal contact time: ${prediction.optimalContactTime || 'N/A'}`);
    console.log(`  - Expected impact: $${prediction.expectedImpact?.toFixed(2) || 'N/A'}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// ANOMALY DETECTION TESTS
// =====================================================

// Test anomaly detection
Deno.test({
  name: "Predictive Analytics - Anomaly Detection",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();

    const startTime = performance.now();
    const results = await predictiveService.detectAnomalies({
      tenantId: TEST_TENANT_ID,
      anomalyTypes: ['revenue_spike', 'traffic_drop', 'conversion_anomaly'],
      severityThreshold: 0.7,
      timeWindowHours: 24,
      includeContext: true
    });
    const executionTime = performance.now() - startTime;

    // Validate anomaly detection results
    assertExists(results);
    // Note: Results might be empty if no anomalies detected, which is normal

    if (results.length > 0) {
      const anomaly = results[0];
      assertExists(anomaly.anomalyId);
      assertExists(anomaly.anomalyType);
      assertExists(anomaly.anomalyScore);
      assertExists(anomaly.severityLevel);
      assertExists(anomaly.anomalyDescription);
      assertExists(anomaly.detectedAt);

      // Validate anomaly values
      assert(anomaly.anomalyScore >= 0 && anomaly.anomalyScore <= 1, "Anomaly score should be between 0 and 1");
      assert(['low', 'medium', 'high', 'critical'].includes(anomaly.severityLevel), "Severity level should be valid");
      assert(['revenue_spike', 'traffic_drop', 'conversion_anomaly'].includes(anomaly.anomalyType), "Anomaly type should be valid");

      if (anomaly.expectedValue && anomaly.actualValue) {
        assert(anomaly.deviationPercentage >= 0, "Deviation percentage should be non-negative");
      }

      const criticalAnomalies = results.filter(a => a.severityLevel === 'critical').length;
      const avgAnomalyScore = results.reduce((sum, a) => sum + a.anomalyScore, 0) / results.length;

      console.log(`✓ Anomaly detection completed in ${executionTime.toFixed(2)}ms`);
      console.log(`  - Anomalies detected: ${results.length}`);
      console.log(`  - Critical anomalies: ${criticalAnomalies}`);
      console.log(`  - Average anomaly score: ${avgAnomalyScore.toFixed(4)}`);
      console.log(`  - Types detected: ${[...new Set(results.map(a => a.anomalyType))].join(', ')}`);
    } else {
      console.log(`✓ Anomaly detection completed in ${executionTime.toFixed(2)}ms - No anomalies detected (normal)`);
    }

    assert(executionTime < 1000, `Anomaly detection should be efficient (${executionTime.toFixed(2)}ms)`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test anomaly detection with different thresholds
Deno.test({
  name: "Predictive Analytics - Anomaly Detection Thresholds",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();
    const thresholds = [0.5, 0.7, 0.9];

    for (const threshold of thresholds) {
      const startTime = performance.now();
      const results = await predictiveService.detectAnomalies({
        tenantId: TEST_TENANT_ID,
        severityThreshold: threshold,
        timeWindowHours: 12
      });
      const executionTime = performance.now() - startTime;

      assertExists(results);
      assert(executionTime < 500, `Anomaly detection with threshold ${threshold} should be fast`);

      // Higher thresholds should generally return fewer anomalies
      console.log(`✓ Threshold ${threshold}: ${results.length} anomalies detected (${executionTime.toFixed(2)}ms)`);
    }
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// =====================================================
// PERFORMANCE AND INTEGRATION TESTS
// =====================================================

// Test prediction performance under load
Deno.test({
  name: "Predictive Analytics - Performance Under Load",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();
    const iterations = 10;
    const executionTimes: number[] = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      await predictiveService.generatePrediction({
        tenantId: TEST_TENANT_ID,
        modelType: 'churn_prediction',
        entityId: `customer_${i}`,
        entityType: 'customer'
      });
      const executionTime = performance.now() - startTime;
      executionTimes.push(executionTime);
    }

    const avgExecutionTime = executionTimes.reduce((sum, time) => sum + time, 0) / iterations;
    const maxExecutionTime = Math.max(...executionTimes);
    const minExecutionTime = Math.min(...executionTimes);

    // Performance assertions
    assert(avgExecutionTime < 500, `Average execution time should be under 500ms (${avgExecutionTime.toFixed(2)}ms)`);
    assert(maxExecutionTime < 1000, `Max execution time should be under 1000ms (${maxExecutionTime.toFixed(2)}ms)`);

    console.log(`✓ Performance test completed - ${iterations} predictions`);
    console.log(`  - Average execution time: ${avgExecutionTime.toFixed(2)}ms`);
    console.log(`  - Min execution time: ${minExecutionTime.toFixed(2)}ms`);
    console.log(`  - Max execution time: ${maxExecutionTime.toFixed(2)}ms`);
    console.log(`  - Throughput: ${(1000 / avgExecutionTime).toFixed(2)} predictions/second`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Test comprehensive ML pipeline
Deno.test({
  name: "Predictive Analytics - Comprehensive ML Pipeline",
  async fn() {
    const predictiveService = new PredictiveAnalyticsService();

    const startTime = performance.now();

    // Test all major prediction types
    const churnPrediction = await predictiveService.predictCustomerChurn({
      tenantId: TEST_TENANT_ID,
      customerId: TEST_CUSTOMER_ID
    });

    const revenueForecast = await predictiveService.generateRevenueForecast({
      tenantId: TEST_TENANT_ID,
      forecastType: 'daily',
      forecastHorizonDays: 7
    });

    const behaviorPrediction = await predictiveService.predictCustomerBehavior({
      tenantId: TEST_TENANT_ID,
      customerId: TEST_CUSTOMER_ID,
      behaviorTypes: ['next_purchase']
    });

    const anomalyDetection = await predictiveService.detectAnomalies({
      tenantId: TEST_TENANT_ID,
      timeWindowHours: 6
    });

    const executionTime = performance.now() - startTime;

    // Validate all predictions completed successfully
    assertExists(churnPrediction);
    assertExists(revenueForecast);
    assertExists(behaviorPrediction);
    assertExists(anomalyDetection);

    assert(churnPrediction.length > 0, "Churn prediction should return results");
    assert(revenueForecast.length > 0, "Revenue forecast should return results");
    assert(behaviorPrediction.length > 0, "Behavior prediction should return results");
    // Anomaly detection may return empty results, which is normal

    assert(executionTime < 2000, `Complete ML pipeline should be efficient (${executionTime.toFixed(2)}ms)`);

    console.log(`✓ Comprehensive ML pipeline completed in ${executionTime.toFixed(2)}ms`);
    console.log(`  - Churn predictions: ${churnPrediction.length}`);
    console.log(`  - Revenue forecasts: ${revenueForecast.length}`);
    console.log(`  - Behavior predictions: ${behaviorPrediction.length}`);
    console.log(`  - Anomalies detected: ${anomalyDetection.length}`);
  },
  sanitizeResources: false,
  sanitizeOps: false,
});

// Cleanup test environment
Deno.test({
  name: "Predictive Analytics Service - Cleanup",
  async fn() {
    await closeDatabase();
    await closeRedis();
    console.log("✓ Test environment cleaned up successfully");
  },
  sanitizeResources: false,
  sanitizeOps: false,
});
