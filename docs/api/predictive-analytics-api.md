# Predictive Analytics & Machine Learning API Specification
## Phase 2 Week 15-16: Advanced ML Pipeline API Documentation

### 🎯 API Overview

The Predictive Analytics API provides comprehensive machine learning capabilities including churn prediction, revenue forecasting, behavior prediction, and anomaly detection. All endpoints follow RESTful design principles with comprehensive validation, error handling, and performance monitoring.

**Base URL:** `/api/enhanced-analytics/predictions`

**Authentication:** Bearer token with tenant validation

**Rate Limiting:** Applied to all endpoints

### 📊 API Endpoints Summary

| Endpoint | Method | Purpose | Performance Target |
|----------|--------|---------|-------------------|
| `/generate` | POST | Generate ML predictions | <500ms |
| `/batch` | POST | Batch prediction processing | <2000ms |
| `/churn` | GET | Customer churn prediction | <500ms |
| `/churn/summary` | GET | Churn analysis summary | <1000ms |
| `/revenue-forecast` | GET | Revenue forecasting | <1000ms |
| `/revenue-forecast/accuracy` | GET | Forecast accuracy metrics | <500ms |
| `/behavior` | GET | Customer behavior prediction | <500ms |
| `/anomalies` | GET | Anomaly detection | <1000ms |
| `/anomalies/summary` | GET | Anomaly analysis summary | <500ms |
| `/models` | GET | ML model management | <200ms |
| `/health` | GET | Service health check | <100ms |

---

## 🔮 General Prediction Endpoints

### Generate ML Prediction

**Endpoint:** `POST /api/enhanced-analytics/predictions/generate`

**Description:** Generate predictions using specified ML model with confidence scoring and optional explanations.

**Request Body:**
```json
{
  "tenantId": "uuid",
  "modelType": "churn_prediction|revenue_forecasting|behavior_prediction|anomaly_detection",
  "modelId": "uuid (optional)",
  "entityId": "uuid (optional)",
  "entityType": "string (optional)",
  "predictionHorizonDays": "number (1-365, optional)",
  "includeConfidence": "boolean (optional)",
  "includeExplanation": "boolean (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "predictionId": "uuid",
    "predictedValue": "number",
    "confidenceScore": "number (0-1)",
    "predictionType": "string",
    "predictionMetadata": "object",
    "featureImportance": "object (optional)",
    "explanation": "array (optional)",
    "predictedAt": "ISO 8601 timestamp",
    "validUntil": "ISO 8601 timestamp (optional)"
  },
  "metadata": {
    "executionTime": "string (ms)",
    "modelType": "string",
    "confidenceScore": "number"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

**Example Request:**
```bash
curl -X POST /api/enhanced-analytics/predictions/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "tenantId": "00000000-0000-0000-0000-000000000001",
    "modelType": "churn_prediction",
    "entityId": "customer_001",
    "entityType": "customer",
    "predictionHorizonDays": 30,
    "includeConfidence": true
  }'
```

### Batch Prediction Processing

**Endpoint:** `POST /api/enhanced-analytics/predictions/batch`

**Description:** Generate predictions for multiple entities in a single request for improved efficiency.

**Request Body:**
```json
{
  "tenantId": "uuid",
  "modelType": "string",
  "entityIds": "array of uuids (1-1000)",
  "predictionHorizonDays": "number (optional)",
  "includeConfidence": "boolean (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "data": "array of prediction objects",
  "metadata": {
    "executionTime": "string (ms)",
    "modelType": "string",
    "entityCount": "number",
    "avgConfidence": "string"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

---

## 🚨 Churn Prediction Endpoints

### Customer Churn Prediction

**Endpoint:** `GET /api/enhanced-analytics/predictions/churn`

**Description:** Predict customer churn risk with detailed analysis and intervention recommendations.

**Query Parameters:**
- `tenantId` (required): Tenant UUID
- `customerId` (optional): Specific customer UUID
- `includeRiskFactors` (optional): Include risk factor analysis
- `includeRecommendations` (optional): Include intervention recommendations
- `riskThreshold` (optional): Risk threshold (0-1)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "customerId": "uuid",
      "churnProbability": "number (0-1)",
      "riskLevel": "low|medium|high|critical",
      "timeToChurnDays": "number (optional)",
      "primaryRiskFactors": "array of strings",
      "engagementScore": "number (0-1)",
      "recencyScore": "number (0-1)",
      "frequencyScore": "number (0-1)",
      "monetaryScore": "number (0-1)",
      "recommendedActions": "array of strings",
      "interventionPriority": "number (1-10)",
      "predictedAt": "ISO 8601 timestamp"
    }
  ],
  "metadata": {
    "executionTime": "string (ms)",
    "customersAnalyzed": "number",
    "highRiskCustomers": "number",
    "avgChurnProbability": "number"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

### Churn Prediction Summary

**Endpoint:** `GET /api/enhanced-analytics/predictions/churn/summary`

**Description:** Get aggregated churn prediction statistics and risk distribution.

**Query Parameters:**
- `tenantId` (required): Tenant UUID

**Response:**
```json
{
  "success": true,
  "data": {
    "totalCustomers": "number",
    "riskDistribution": {
      "critical": "number",
      "high": "number",
      "medium": "number",
      "low": "number"
    },
    "avgChurnProbability": "number",
    "highRiskCustomers": "number",
    "interventionRequired": "number"
  },
  "metadata": {
    "executionTime": "string (ms)",
    "generatedAt": "ISO 8601 timestamp"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

---

## 💰 Revenue Forecasting Endpoints

### Revenue Forecast Generation

**Endpoint:** `GET /api/enhanced-analytics/predictions/revenue-forecast`

**Description:** Generate revenue forecasts with confidence intervals and component analysis.

**Query Parameters:**
- `tenantId` (required): Tenant UUID
- `forecastType` (required): daily|weekly|monthly|quarterly
- `forecastHorizonDays` (required): Number of days to forecast (1-365)
- `includeConfidenceInterval` (optional): Include confidence bounds
- `includeComponents` (optional): Include trend/seasonal components
- `algorithm` (optional): arima|prophet|lstm|ensemble

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "forecastName": "string",
      "forecastType": "string",
      "forecastedRevenue": "number",
      "lowerBound": "number (optional)",
      "upperBound": "number (optional)",
      "confidenceLevel": "number",
      "trendComponent": "number (optional)",
      "seasonalComponent": "number (optional)",
      "residualComponent": "number (optional)",
      "forecastAccuracy": "number (optional)",
      "forecastDate": "date string",
      "createdAt": "ISO 8601 timestamp"
    }
  ],
  "metadata": {
    "executionTime": "string (ms)",
    "forecastType": "string",
    "forecastPoints": "number",
    "totalForecastedRevenue": "number",
    "avgAccuracy": "number",
    "algorithm": "string"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

### Forecast Accuracy Metrics

**Endpoint:** `GET /api/enhanced-analytics/predictions/revenue-forecast/accuracy`

**Description:** Get historical forecast accuracy metrics and validation statistics.

**Query Parameters:**
- `tenantId` (required): Tenant UUID
- `forecastType` (optional): Forecast type to analyze

**Response:**
```json
{
  "success": true,
  "data": {
    "avgAccuracy": "number",
    "minAccuracy": "number",
    "maxAccuracy": "number",
    "forecastCount": "number",
    "confidenceLevel": "number"
  },
  "metadata": {
    "executionTime": "string (ms)",
    "forecastType": "string",
    "evaluationPeriod": "string"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

---

## 🎯 Behavior Prediction Endpoints

### Customer Behavior Prediction

**Endpoint:** `GET /api/enhanced-analytics/predictions/behavior`

**Description:** Predict customer behavior and generate next-best-action recommendations.

**Query Parameters:**
- `tenantId` (required): Tenant UUID
- `customerId` (required): Customer UUID
- `behaviorTypes` (optional): Array of behavior types to predict
- `includeRecommendations` (optional): Include action recommendations
- `includeTimingPredictions` (optional): Include timing predictions

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "customerId": "uuid",
      "behaviorType": "string",
      "predictedBehavior": "object",
      "confidenceScore": "number (0-1)",
      "recommendedAction": "string (optional)",
      "actionPriority": "number (1-10)",
      "expectedImpact": "number (optional)",
      "predictedTiming": "ISO 8601 timestamp (optional)",
      "optimalContactTime": "ISO 8601 timestamp (optional)",
      "recommendedProducts": "array of strings",
      "recommendedChannels": "array of strings",
      "predictedAt": "ISO 8601 timestamp"
    }
  ],
  "metadata": {
    "executionTime": "string (ms)",
    "customerId": "uuid",
    "predictionsGenerated": "number",
    "avgConfidence": "number",
    "highPriorityActions": "number"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

---

## 🚨 Anomaly Detection Endpoints

### Anomaly Detection

**Endpoint:** `GET /api/enhanced-analytics/predictions/anomalies`

**Description:** Detect anomalies in real-time data streams with severity classification.

**Query Parameters:**
- `tenantId` (required): Tenant UUID
- `anomalyTypes` (optional): Array of anomaly types to detect
- `severityThreshold` (optional): Minimum severity threshold (0-1)
- `timeWindowHours` (optional): Time window for analysis (1-168)
- `includeContext` (optional): Include contextual information

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "anomalyId": "uuid",
      "anomalyType": "string",
      "entityType": "string (optional)",
      "entityId": "uuid (optional)",
      "anomalyScore": "number (0-1)",
      "severityLevel": "low|medium|high|critical",
      "anomalyDescription": "string",
      "expectedValue": "number (optional)",
      "actualValue": "number (optional)",
      "deviationPercentage": "number",
      "contributingFactors": "object",
      "affectedMetrics": "array of strings",
      "detectedAt": "ISO 8601 timestamp"
    }
  ],
  "metadata": {
    "executionTime": "string (ms)",
    "anomaliesDetected": "number",
    "criticalAnomalies": "number",
    "avgAnomalyScore": "number",
    "timeWindowHours": "number"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

### Anomaly Detection Summary

**Endpoint:** `GET /api/enhanced-analytics/predictions/anomalies/summary`

**Description:** Get aggregated anomaly detection statistics and severity distribution.

**Query Parameters:**
- `tenantId` (required): Tenant UUID
- `timeWindowHours` (optional): Time window for analysis

**Response:**
```json
{
  "success": true,
  "data": {
    "totalAnomalies": "number",
    "severityDistribution": {
      "critical": "number",
      "high": "number",
      "medium": "number",
      "low": "number"
    },
    "typeDistribution": "object",
    "avgAnomalyScore": "number",
    "criticalAnomalies": "number",
    "requiresAttention": "number"
  },
  "metadata": {
    "executionTime": "string (ms)",
    "timeWindowHours": "number",
    "generatedAt": "ISO 8601 timestamp"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

---

## 🔧 Model Management Endpoints

### ML Models

**Endpoint:** `GET /api/enhanced-analytics/predictions/models`

**Description:** Get available ML models and their status information.

**Query Parameters:**
- `tenantId` (required): Tenant UUID
- `modelType` (optional): Filter by model type

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "modelName": "string",
      "modelType": "string",
      "algorithm": "string",
      "status": "string",
      "isProduction": "boolean",
      "trainingAccuracy": "number",
      "validationAccuracy": "number",
      "createdAt": "ISO 8601 timestamp"
    }
  ],
  "metadata": {
    "executionTime": "string (ms)",
    "modelCount": "number",
    "activeModels": "number",
    "productionModels": "number"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

### Health Check

**Endpoint:** `GET /api/enhanced-analytics/predictions/health`

**Description:** Service health check for monitoring and diagnostics.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "service": "predictive-analytics",
    "version": "1.0.0",
    "uptime": "number",
    "timestamp": "ISO 8601 timestamp"
  },
  "metadata": {
    "executionTime": "string (ms)"
  }
}
```

---

## 🚨 Error Handling

### Error Response Format

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": "object (optional - validation errors)",
  "timestamp": "ISO 8601 timestamp"
}
```

### HTTP Status Codes

- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

### Common Error Scenarios

1. **Invalid Tenant ID**: 400 with validation details
2. **Missing Required Parameters**: 400 with parameter list
3. **Rate Limit Exceeded**: 429 with retry information
4. **Model Not Found**: 404 with model information
5. **Service Unavailable**: 500 with error details

---

## 📊 Performance & Monitoring

### Response Time Targets

- **Simple Queries**: <200ms (health, models)
- **Predictions**: <500ms (churn, behavior)
- **Complex Analysis**: <1000ms (forecasting, anomalies)
- **Batch Processing**: <2000ms (batch predictions)

### Monitoring Metrics

All responses include execution time in metadata for performance monitoring and optimization.

### Rate Limiting

- **Default Limit**: 1000 requests per 15-minute window
- **Burst Capacity**: 100 requests per minute
- **Headers**: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

---

## 🔐 Security & Authentication

### Authentication

All endpoints require Bearer token authentication with tenant validation.

### Authorization

- **Tenant Isolation**: All data access is restricted by tenant ID
- **Row-Level Security**: Database-level tenant isolation
- **API Validation**: Tenant access validation on all endpoints

### Data Privacy

- **GDPR Compliance**: Data handling follows GDPR requirements
- **Data Retention**: Automated data lifecycle management
- **Audit Logging**: Comprehensive access and modification logging
